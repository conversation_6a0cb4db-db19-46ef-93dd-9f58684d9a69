const analyticsService = require("../services/analyticsService");
const TeamMember = require("../models/teamMember");

/**
 * Get YouTube analytics data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getYouTubeAnalytics = async (req, res) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("analytics")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to analytics this owner's account.",
          permission: false,
        });
      }
    }
    const timeframe = req.query.timeframe || "7days";
    // Validate user ID
    if (!ownerId) {
      return res.status(400).json({
        success: false,
        message: "User ID not found in request",
      });
    }

    // Validate timeframe
    const validTimeframes = ["7days", "30days", "90days"];
    if (!validTimeframes.includes(timeframe)) {
      return res.status(400).json({
        success: false,
        message: `Invalid timeframe. Allowed values are: ${validTimeframes.join(
          ", "
        )}`,
      });
    }

    // Call your service to fetch analytics
    const analyticsData = await analyticsService.getYouTubeAnalytics(
      ownerId,
      timeframe
    );

    return res.status(200).json({
      success: true,
      data: analyticsData,
    });
  } catch (error) {
    console.error("Error in getYouTubeAnalytics controller:", error);

    // Specific handling for auth or connection issues
    const errorMessage = error?.message || "";

    if (
      ["not found", "not connected", "auth", "token"].some((substr) =>
        errorMessage.includes(substr)
      )
    ) {
      return res.status(401).json({
        success: false,
        message: "YouTube account not connected or authentication expired",
      });
    }

    // Generic error response
    return res.status(500).json({
      success: false,
      message: "Failed to fetch YouTube analytics",
      error: errorMessage,
    });
  }
};

/**
 * Get YouTube channel data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getChannelData = async (req, res) => {
  try {
    const channelId = req.query.channelId || null;
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("analytics")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to analytics this owner's account.",
          permission: false,
        });
      }
    }

    const channelData = await analyticsService.getChannelData(
      ownerId,
      channelId
    );

    return res.status(200).json({
      success: true,
      data: channelData,
    });
  } catch (error) {
    console.error("Error in getChannelData controller:", error);

    // Handle authentication errors
    if (
      error.message &&
      (error.message.includes("not found") ||
        error.message.includes("not connected") ||
        error.message.includes("auth") ||
        error.message.includes("token"))
    ) {
      return res.status(401).json({
        success: false,
        message: "YouTube account not connected or authentication expired",
      });
    }

    return res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch channel data",
    });
  }
};

// Export other controller functions as needed
