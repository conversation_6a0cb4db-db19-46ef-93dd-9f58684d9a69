const User = require("../models/user");
const Plan = require("../models/plan");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const { google } = require("googleapis");
const { OAuth2Client } = require("google-auth-library");
const authenticateWithGoogle = require("../helper/authenticate-google");
const { log } = require("console");
const createOAuthClient = require("../helper/OAuthClient");
const tokenManager = require("../services/tokenManager");
const { getPlans } = require("./subscriptionController");
const fetch = (...args) => import('node-fetch').then(mod => mod.default(...args)); // <-- Use dynamic import
const mongoose = require("mongoose");

// Create OAuth2 client
const oauth2Client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_CALLBACK_URL
);

// Register a new user
exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(400)
        .json({ success: false, message: "User with this email already exists" });
    }

    // Create new user
    const user = new User({
      name,
      email,
      password, // Password will be hashed in the model's pre-save hook
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "7d",
    });

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    res
      .status(500)
      .json({ success: false, message: "Registration failed", error: error.message });
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ email }).select("+password");
    if (!user) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "7d",
    });

    res.status(200).json({
      message: "Login successful",
      success: true,
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Login failed", error: error.message });
  }
};

// Refresh token
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: "Refresh token is required" });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    // Generate new access token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "7d",
    });

    res.status(200).json({
      message: "Token refreshed successfully",
      token,
    });
  } catch (error) {
    console.error("Token refresh error:", error);
    res
      .status(401)
      .json({ message: "Invalid refresh token", error: error.message });
  }
};

// Get user plan
exports.getUserPlan = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;

    const user = await User.findById(userId).populate("subscription.plan");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (!user.subscription || !user.subscription.plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // Return the populated plan document
    res.status(200).json({
      success: true,
      plan: user.subscription.plan,
    });
  } catch (error) {
    console.error("Get user plan error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get user plan",
      error: error.message,
    });
  }
};

// Logout user
exports.logout = async (req, res) => {
  try {
    // In a stateless JWT authentication system, the client is responsible for
    // discarding the token. The server can't invalidate the token directly.
    // You could implement a token blacklist if needed.

    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({ message: "Logout failed", error: error.message });
  }
};

// Google OAuth authentication
exports.googleAuth = async (req, res) => {
  try {
    // Redirect to Google OAuth URL
    const authUrl = await getGoogleAuthUrl();
    res.redirect(authUrl);
  } catch (error) {
    console.error("Google auth error:", error);
    res.status(500).json({
      message: "Failed to initiate Google authentication",
      error: error.message,
    });
  }
};

// Google OAuth callback
exports.googleCallback = async (req, res) => {
  try {
    const { code, state } = req.query;

    if (!code) {
      return res
        .status(400)
        .json({ message: "Authorization code is required" });
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL ||
        "http://localhost:8000/api/auth/oauth2callback"
    );

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Get user info
    const oauth2 = google.oauth2({
      version: "v2",
      auth: oauth2Client,
    });

    const userInfoResponse = await oauth2.userinfo.get();
    const userInfo = userInfoResponse.data;

    // Get YouTube channel info
    let channelInfo;
    try {
      const youtube = google.youtube({
        version: "v3",
        auth: oauth2Client,
      });

      const channelResponse = await youtube.channels.list({
        part: "snippet,contentDetails,statistics",
        mine: true,
      });

      if (channelResponse.data.items && channelResponse.data.items.length > 0) {
        channelInfo = channelResponse.data.items[0];
      }
    } catch (channelError) {
      console.error("Error fetching channel info:", channelError.message);
    }

    // Find or create user
    let user = await User.findOne({ email: userInfo.email });

    if (!user) {
      // Create new user
      user = new User({
        name: userInfo.name,
        email: userInfo.email,
        googleId: userInfo.id,
        googleAccessToken: tokens.access_token,
        googleRefreshToken: tokens.refresh_token,
        googleTokenExpiry: tokens.expiry_date || new Date(Date.now() + (tokens.expires_in || 3600) * 1000),
        youtubeConnected: true,
        youtubeTokenExpiry: tokens.expiry_date || new Date(Date.now() + (tokens.expires_in || 3600) * 1000),
        ...(channelInfo && {
          youtubeChannelId: channelInfo.id,
          youtubeChannelTitle: channelInfo.snippet.title,
          youtubeChannelThumbnail: channelInfo.snippet.thumbnails?.default?.url,
        }),
      });
    } else {
      // Update existing user
      user.googleId = userInfo.id;
      user.googleAccessToken = tokens.access_token;
      user.googleRefreshToken = tokens.refresh_token || user.googleRefreshToken;
      user.googleTokenExpiry = tokens.expiry_date || new Date(Date.now() + (tokens.expires_in || 3600) * 1000);
      user.youtubeConnected = true;
      user.youtubeTokenExpiry = tokens.expiry_date || new Date(Date.now() + (tokens.expires_in || 3600) * 1000);
      if (channelInfo) {
        user.youtubeChannelId = channelInfo.id;
        user.youtubeChannelTitle = channelInfo.snippet.title;
        user.youtubeChannelThumbnail = channelInfo.snippet.thumbnails?.default?.url;
      }
    }

    await user.save();

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "7d",
    });

    // Redirect to frontend with token
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback?token=${token}`);
  } catch (error) {
    console.error("Google callback error:", error);
    res.status(500).json({
      message: "Google authentication callback failed",
      error: error.message,
    });
  }
};

// Get Google Auth URL for YouTube
exports.getGoogleAuthUrl = async (req, res) => {
  try {
    // Get user ID from request or query
    let userId = null;
    if (req.user) {
      userId = req.user.id;
    } else if (req.query.userId) {
      userId = req.query.userId;
    }

    // Create OAuth2 client with application credentials
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL ||
        "http://localhost:8000/api/auth/oauth2callback"
    );

    const scopes = [
      "https://www.googleapis.com/auth/youtube",
      "https://www.googleapis.com/auth/youtube.upload",
      "https://www.googleapis.com/auth/userinfo.profile",
      "https://www.googleapis.com/auth/userinfo.email",
    ];

    // Generate auth URL with user ID as state parameter
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent", // Force consent screen to get refresh token
      state: userId || undefined, // Pass user ID to callback
    });

    console.log("Generated Google auth URL:", authUrl);
    return res.status(200).json({
      success: true,
      url: authUrl,
    });
  } catch (error) {
    console.error("Get Google auth URL error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate Google auth URL",
      error: error.message,
    });
  }
};

// Handle Google OAuth callback
exports.handleGoogleCallback = async (req, res) => {
  try {
    const { code, state } = req.query;

    if (!code) {
      return res.redirect(
        `${process.env.FRONTEND_URL}/youtube-callback?success=false&error=No authorization code received`
      );
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL ||
        "http://localhost:8000/api/auth/oauth2callback"
    );

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Get user info
    const oauth2 = google.oauth2({
      version: "v2",
      auth: oauth2Client,
    });

    const userInfo = await oauth2.userinfo.get();

    // Get YouTube channel info
    let channelInfo;
    try {
      const youtube = google.youtube({
        version: "v3",
        auth: oauth2Client,
      });

      const channelResponse = await youtube.channels.list({
        part: "snippet,contentDetails,statistics",
        mine: true,
      });

      if (channelResponse.data.items && channelResponse.data.items.length > 0) {
        channelInfo = channelResponse.data.items[0];
      }
    } catch (channelError) {
      console.error("Error fetching channel info:", channelError.message);
    }

    // Get UserId from state
    let userId = state;

    if (userId && userId !== "undefined" && userId !== "null") {
      try {
        if (mongoose.Types.ObjectId.isValid(userId)) {
          const user = await User.findById(userId);
          if (user) {
            // Store tokens
            user.googleAccessToken = tokens.access_token;
            user.googleRefreshToken =
              tokens.refresh_token || user.googleRefreshToken; // Keep existing refresh token if not provided
            user.youtubeConnected = true;
            user.youtubeTokenExpiry = tokens.expiry_date;

            if (channelInfo) {
              user.youtubeChannelId = channelInfo.id;
              user.youtubeChannelTitle = channelInfo.snippet.title;
              user.youtubeChannelThumbnail = channelInfo.snippet.thumbnails?.default?.url;
            }

            await user.save();
            console.log("Updated user with Google tokens:", user._id);
          } else {
            console.log("User not found with ID:", userId);
          }
        } else {
          console.log("Invalid user ID format:", userId);
        }
      } catch (userError) {
        console.error("Error updating user:", userError.message);
      }
    }

    // Prepare channel info for frontend
    let channelInfoForFrontend = null;
    if (channelInfo) {
      channelInfoForFrontend = {
        id: channelInfo.id,
        title: channelInfo.snippet.title,
        thumbnail: channelInfo.snippet.thumbnails?.default?.url,
        subscriberCount: channelInfo.statistics?.subscriberCount,
        videoCount: channelInfo.statistics?.videoCount,
      };
    }

    // Redirect to frontend with success message and channel info
    let redirectUrl = `${process.env.FRONTEND_URL}/youtube-callback?success=true`;

    // Add channel info to redirect URL if available
    if (channelInfoForFrontend) {
      redirectUrl += `&channelInfo=${encodeURIComponent(
        JSON.stringify(channelInfoForFrontend)
      )}`;
    }

    res.redirect(redirectUrl);
  } catch (error) {
    console.error("Google callback error:", error);
    res.redirect(
      `${
        process.env.FRONTEND_URL
      }/youtube-callback?success=false&error=${encodeURIComponent(
        error.message
      )}`
    );
  }
};

// Save YouTube credentials
exports.saveCredentials = async (req, res) => {
  try {
    const { CLIENT_ID, CLIENT_SECRET, API_KEY, UserId } = req.body;

    if (!CLIENT_ID || !CLIENT_SECRET || !API_KEY) {
      return res.status(400).json({
        success: false,
        message: "All credential fields are required",
      });
    }

    // Find user - try multiple ways to get the user ID
    let userId = UserId;

    // If no UserId provided, try to get from authenticated user
    if (!userId && req.user) {
      userId = req.user.id || req.user.userId;
    }

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Save credentials to user
    user.googleClientId = CLIENT_ID;
    user.googleClientSecret = CLIENT_SECRET;
    user.googleApiKey = API_KEY;

    await user.save();

    res.status(200).json({
      success: true,
      message: "YouTube credentials saved successfully",
    });
  } catch (error) {
    console.error("Save credentials error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to save YouTube credentials",
      error: error.message,
    });
  }
};

// Disconnect YouTube
exports.disconnectYoutube = async (req, res) => {
  try {
    // User is attached to req by authenticate middleware
    const user = req.user;

    // Clear Google credentials
    user.googleAccessToken = undefined;
    user.googleRefreshToken = undefined;
    user.youtubeConnected = false;
    user.youtubeChannelId = undefined;
    user.youtubeChannelName = undefined;

    await user.save();

    res.status(200).json({ message: "YouTube disconnected successfully" });
  } catch (error) {
    console.error("Disconnect YouTube error:", error);
    res
      .status(500)
      .json({ message: "Failed to disconnect YouTube", error: error.message });
  }
};

// Update YouTube channel info
exports.updateYoutubeChannel = async (req, res) => {
  try {
    // Get user from request (set by authenticate middleware)
    const user = req.user;

    // Check if user has Google tokens
    if (!user.googleAccessToken || !user.googleRefreshToken) {
      return res.status(400).json({
        success: false,
        message: "Google authentication required",
      });
    }

    // Get a fresh OAuth client using tokenManager
    const oauth2Client = await tokenManager.getAuthClient(user._id);

    if (!oauth2Client) {
      return res.status(401).json({
        success: false,
        message:
          "Failed to get authenticated client. Please reconnect your YouTube account.",
      });
    }

    // Get YouTube channel info
    const youtube = google.youtube({ version: "v3", auth: oauth2Client });
    const response = await youtube.channels.list({
      part: "snippet,contentDetails,statistics",
      mine: true,
    });

    if (!response.data.items || response.data.items.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No YouTube channel found",
      });
    }

    const channel = response.data.items[0];

    // Update user with channel info
    user.youtubeChannelId = channel.id;
    user.youtubeChannelName = channel.snippet.title;
    user.youtubeChannelDescription = channel.snippet.description || "";
    user.youtubeChannelCustomUrl = channel.snippet.customUrl || "";
    user.youtubeChannelThumbnail =
      channel.snippet.thumbnails?.default?.url || null;

    // Update statistics
    user.youtubeChannelStatistics = {
      subscriberCount: channel.statistics?.subscriberCount || "0",
      videoCount: channel.statistics?.videoCount || "0",
      viewCount: channel.statistics?.viewCount || "0",
    };

    await user.save();

    res.status(200).json({
      success: true,
      message: "YouTube channel updated successfully",
      channelInfo: {
        id: channel.id,
        title: channel.snippet.title,
        description: channel.snippet.description || "",
        customUrl: channel.snippet.customUrl || "",
        thumbnail: channel.snippet.thumbnails?.default?.url || null,
        statistics: {
          subscriberCount: channel.statistics?.subscriberCount || "0",
          videoCount: channel.statistics?.videoCount || "0",
          viewCount: channel.statistics?.viewCount || "0",
        },
        connectedAt: user.youtubeConnectedAt || new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error updating YouTube channel:", error);

    // Check if it's an auth error
    if (
      error.code === 401 ||
      (error.response && error.response.status === 401)
    ) {
      return res.status(401).json({
        success: false,
        message: "YouTube authentication required",
        authRequired: true,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to update YouTube channel",
      error: error.message,
    });
  }
};

// Check Google auth status
exports.checkGoogleAuth = async (req, res) => {
  try {
    // User is attached to req by authenticate middleware
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Check if YouTube is connected
    const youtubeConnected =
      !!user.youtubeConnected && !!user.googleAccessToken;

    // Check if token needs refresh
    const tokenNeedsRefresh =
      youtubeConnected &&
      user.youtubeTokenExpiry &&
      new Date(user.youtubeTokenExpiry) <= new Date();

    // Prepare channel info if available
    let channelInfo = null;
    if (youtubeConnected && user.youtubeChannelId) {
      channelInfo = {
        id: user.youtubeChannelId,
        title: user.youtubeChannelName,
        description: user.youtubeChannelDescription || "",
        customUrl: user.youtubeChannelCustomUrl || "",
        thumbnail: user.youtubeChannelThumbnail,
        statistics: user.youtubeChannelStatistics || {
          subscriberCount: "0",
          videoCount: "0",
          viewCount: "0",
        },
        connectedAt: user.youtubeConnectedAt || new Date().toISOString(),
      };
    }

    res.status(200).json({
      success: true,
      youtubeConnected,
      tokenNeedsRefresh,
      channelInfo,
    });
  } catch (error) {
    console.error("Check Google auth error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to check Google auth status",
      error: error.message,
    });
  }
};

// Forgot password
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Generate random reset token
    const resetToken = crypto.randomBytes(32).toString("hex");

    // Hash token before saving to database
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Save hashed token to user document with expiration
    user.resetPasswordToken = hashedToken;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();

    try {
      res.status(200).json({
        success: true,
        message: "Password reset email sent successfully",
      });
    } catch (emailError) {
      // If email fails, revert the token
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      await user.save();

      console.error("Error sending reset email:", emailError);
      return res.status(500).json({
        success: false,
        message: "Failed to send password reset email",
      });
    }
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process password reset request",
      error: error.message,
    });
  }
};

// Reset password
exports.resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({
        success: false,
        message: "Token and password are required",
      });
    }

    // Hash the token from the request to compare with stored hashed token
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    // Find user by reset token and check if token is expired
    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired password reset token",
      });
    }

    // Update password
    user.password = password; // Password will be hashed in the model's pre-save hook
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to reset password",
      error: error.message,
    });
  }
};

// change password
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: "Current password and new password are required",
      });
    }

    // User is attached to req by authenticate middleware
    const userId = req.user.userId || req.user.id;

    // Find user with password included
    const user = await User.findById(userId).select("+password");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (!user.password) {
      return res.status(400).json({
        success: false,
        message: "Cannot change password for accounts without a password",
      });
    }

    // Check current password
    const isPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password
    );
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Update password
    user.password = newPassword; // Password will be hashed in the model's pre-save hook
    await user.save();

    res.status(200).json({
      success: true,
      message: "Password changed successfully",
    });
  } catch (error) {
    console.error("Change password error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to change password",
      error: error.message,
    });
  }
};

// Update YouTube settings
exports.updateYoutubeSettings = async (req, res) => {
  try {
    const { defaultPrivacyStatus } = req.body;
    const userId = req.query.ownerId || req.user.id;

    // Validate privacy status
    const validPrivacyStatuses = ["private", "public", "unlisted"];
    if (
      defaultPrivacyStatus &&
      !validPrivacyStatuses.includes(defaultPrivacyStatus)
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid privacy status. Must be one of: private, public, unlisted",
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Update user settings
    if (!user.youtubeSettings) {
      user.youtubeSettings = {};
    }

    if (defaultPrivacyStatus) {
      user.youtubeSettings.defaultPrivacyStatus = defaultPrivacyStatus;
    }

    await user.save();

    res.status(200).json({
      success: true,
      message: "YouTube settings updated successfully",
      settings: user.youtubeSettings,
    });
  } catch (error) {
    console.error("Update YouTube settings error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update YouTube settings",
      error: error.message,
    });
  }
};

// Generate YouTube auth URL
exports.getYouTubeAuthUrl = async (req, res) => {
  try {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    const scopes = [
      "https://www.googleapis.com/auth/youtube.upload",
      "https://www.googleapis.com/auth/youtube",
    ];

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent", // Force to get refresh token
    });

    res.status(200).json({ authUrl });
  } catch (error) {
    console.error("Error generating YouTube auth URL:", error);
    res.status(500).json({
      message: "Failed to generate YouTube auth URL",
      error: error.message,
    });
  }
};

// Get user info from Google
const getUserInfo = async (oauth2Client) => {
  const people = google.people({ version: "v1", auth: oauth2Client });
  const response = await people.people.get({
    resourceName: "people/me",
    personFields: "emailAddresses,names,photos",
  });

  const person = response.data;

  return {
    id: person.resourceName.split("/")[1],
    email: person.emailAddresses?.[0]?.value,
    name: person.names?.[0]?.displayName,
    picture: person.photos?.[0]?.url,
  };
};

// Check YouTube connection status
exports.checkYouTubeConnection = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if user has YouTube connected
    const isConnected = user.youtubeConnected || false;

    // Check if token is expired
    const tokenNeedsRefresh =
      isConnected &&
      user.youtubeTokenExpiry &&
      new Date(user.youtubeTokenExpiry) <= new Date();

    // Prepare channel info if connected
    let channelInfo = null;
    if (isConnected) {
      channelInfo = {
        id: user.youtubeChannelId,
        title: user.youtubeChannelTitle || user.youtubeChannelName,
        thumbnail: user.youtubeChannelThumbnail,
        customUrl: user.youtubeChannelCustomUrl,
        statistics: {
          subscriberCount: user.youtubeSubscriberCount,
          videoCount: user.youtubeVideoCount,
          viewCount: user.youtubeViewCount,
        },
        connectedAt: user.youtubeConnectedAt || user.updatedAt,
      };

      // If we have access token and it's not expired, try to refresh channel info
      if (user.googleAccessToken && !tokenNeedsRefresh) {
        try {
          // Create OAuth client
          const oauth2Client = new google.auth.OAuth2(
            process.env.GOOGLE_CLIENT_ID,
            process.env.GOOGLE_CLIENT_SECRET,
            process.env.GOOGLE_CALLBACK_URL
          );

          // Set credentials
          oauth2Client.setCredentials({
            access_token: user.googleAccessToken,
            refresh_token: user.googleRefreshToken,
          });

          // Get YouTube channel info
          const youtube = google.youtube({
            version: "v3",
            auth: oauth2Client,
          });

          const channelResponse = await youtube.channels.list({
            part: "snippet,statistics",
            mine: true,
          });

          if (
            channelResponse.data.items &&
            channelResponse.data.items.length > 0
          ) {
            const channel = channelResponse.data.items[0];

            // Update channel info
            channelInfo = {
              id: channel.id,
              title: channel.snippet.title,
              thumbnail: channel.snippet.thumbnails?.default?.url,
              customUrl: channel.snippet.customUrl,
              statistics: {
                subscriberCount: channel.statistics?.subscriberCount,
                videoCount: channel.statistics?.videoCount,
                viewCount: channel.statistics?.viewCount,
              },
              connectedAt: user.youtubeConnectedAt || user.updatedAt,
            };

            // Update user with latest channel info
            user.youtubeChannelId = channel.id;
            user.youtubeChannelTitle = channel.snippet.title;
            user.youtubeChannelThumbnail =
              channel.snippet.thumbnails?.default?.url;
            user.youtubeChannelCustomUrl = channel.snippet.customUrl;
            user.youtubeSubscriberCount = channel.statistics?.subscriberCount;
            user.youtubeVideoCount = channel.statistics?.videoCount;
            user.youtubeViewCount = channel.statistics?.viewCount;

            await user.save();
          }
        } catch (error) {
          console.error("Error refreshing channel info:", error);
          // Don't fail the request, just use the stored info
        }
      }
    }

    return res.status(200).json({
      success: true,
      youtubeConnected: isConnected,
      tokenNeedsRefresh,
      channelInfo,
    });
  } catch (error) {
    console.error("Error checking YouTube connection:", error);
    return res.status(500).json({
      success: false,
      message: "Error checking YouTube connection",
      error: error.message,
    });
  }
};

// Disconnect YouTube
exports.disconnectYouTube = async (req, res) => {
  try {
    const user = req.user;

    // Clear Google/YouTube credentials
    user.googleAccessToken = undefined;
    user.googleRefreshToken = undefined;
    user.googleTokenExpiry = undefined;
    user.googleEmail = undefined;
    user.googleProfileId = undefined;
    user.googleProfilePicture = undefined;
    user.youtubeConnected = false;

    await user.save();

    res.status(200).json({
      message: "YouTube disconnected successfully",
    });
  } catch (error) {
    console.error("Error disconnecting YouTube:", error);
    res.status(500).json({
      message: "Failed to disconnect YouTube",
      error: error.message,
    });
  }
};

// Update Profile
exports.UpdateProfile = async (req, res) => {
  try {
    const { name, email } = req.body;
    const userId = req.user.id;

    // Validate inputs
    if (!name && !email) {
      return res.status(400).json({
        success: false,
        message: "At least one field (name or email) is required for update",
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if email is being updated and is not already taken
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "Email already in use",
        });
      }
      user.email = email;
    }

    // Update name if provided
    if (name) {
      user.name = name;
    }

    // Save updates
    await user.save();

    res.status(200).json({
      success: true,
      message: "Profile updated successfully",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
      },
    });
  } catch (error) {
    console.error("Update profile error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update profile",
      error: error.message,
    });
  }
};

// Connect Instagram
exports.ConnectInstagram = async (req, res) => {
  try {
    const { username, password } = req.body;
    const userId = req.ownerId || req.user.id;

    if (!username && !password) {
      res.status(400).json({
        success: false,
        message: "Credentials Missing.",
      });
    }

    const userExisting = await User.findById(userId);
    if (!userExisting) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    const encryptedPassword = await bcrypt.hash(password, 10);

    userExisting.instagramUsername = username;
    userExisting.instagramPassword = encryptedPassword;

    // Save updates
    await userExisting.save();

    res.status(200).json({
      success: true,
      message: "Instagram Connected successfully",
    });
  } catch (error) {
    console.error("Connect Instagram error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to Connect Instagram",
      error: error.message,
    });
  }
};
exports.DisconnectInstagram = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    user.instagramUsername = "";
    user.instagramPassword = "";

    await user.save();
    res.status(200).json({
      success: true,
      message: "Instagram Disconnected successfully",
    });
  } catch (error) {
    console.error("Disconnect Instagram error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to Disconnect Instagram",
      error: error.message,
    });
  }
};

// Check Instagram Connection
exports.CheckInstagram = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if Instagram credentials exist
    const isConnected = !!user.instagramUsername;

    res.status(200).json({
      success: true,
      isConnected,
      instagramUsername: isConnected ? user.instagramUsername : null,
    });
  } catch (error) {
    console.error("Check Instagram error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to Check Instagram",
      error: error.message,
    });
  }
};

// Connect Facebook using OAuth
exports.ConnectFacebook = async (req, res) => {
  try {
    const { code } = req.query;
    const userId = req.user.id;

    // Facebook OAuth config
    const clientId = process.env.FACEBOOK_CLIENT_ID;
    const clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
    const redirectUri = process.env.FACEBOOK_REDIRECT_URI;

    if (!code) {
      // Step 1: Redirect to Facebook OAuth dialog
      const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${encodeURIComponent(
        redirectUri
      )}&scope=email,public_profile,pages_show_list,pages_read_engagement&response_type=code&state=${userId}`;
      return res.status(200).json({ url: authUrl });
    }

    // Step 2: Exchange code for access token
    const tokenUrl = `https://graph.facebook.com/v18.0/oauth/access_token?client_id=${clientId}&redirect_uri=${encodeURIComponent(
      redirectUri
    )}&client_secret=${clientSecret}&code=${code}&grant_type=authorization_code`;

    const tokenRes = await fetch(tokenUrl);
    const tokenData = await tokenRes.json();

    if (!tokenRes.ok || !tokenData.access_token) {
      return res.status(400).json({
        success: false,
        message: "Failed to obtain Facebook access token",
        error: tokenData.error || "Invalid response from Facebook",
      });
    }

    // Step 3: Get user info
    const userInfoRes = await fetch(
      `https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${tokenData.access_token}`
    );
    const userInfo = await userInfoRes.json();

    if (!userInfoRes.ok || !userInfo.id) {
      throw new Error("Failed to fetch user info from Facebook");
    }

    // Step 4: Get user's pages (only if needed)
    let page = null;
    const pagesRes = await fetch(
      `https://graph.facebook.com/me/accounts?access_token=${tokenData.access_token}`
    );
    const pagesData = await pagesRes.json();

    if (pagesRes.ok && pagesData.data?.length > 0) {
      page = pagesData.data[0];
      // Optionally fetch page picture
      const pagePicRes = await fetch(
        `https://graph.facebook.com/${page.id}/picture?type=large&redirect=false&access_token=${tokenData.access_token}`
      );
      const pagePicData = await pagePicRes.json();
      page.picture = pagePicData.data?.url;
    }

    // Step 5: Save to database
    const user = await User.findByIdAndUpdate(
      userId,
      {
        facebookConnected: true,
        facebookAccessToken: tokenData.access_token,
        facebookTokenExpiry: Date.now() + (tokenData.expires_in || 0) * 1000,
        facebookUserId: userInfo.id,
        facebookName: userInfo.name,
        facebookEmail: userInfo.email,
        facebookPicture: userInfo.picture?.data?.url,
        ...(page && {
          facebookPageId: page.id,
          facebookPageName: page.name,
          facebookPageAccessToken: page.access_token,
          facebookPagePicture: page.picture,
        }),
      },
      { new: true }
    );

    if (!user) {
      throw new Error("User not found");
    }

    res.status(200).json({
      success: true,
      message: "Facebook connected successfully",
      facebook: {
        id: user.facebookUserId,
        name: user.facebookPageName || user.facebookName,
        email: user.facebookEmail,
        picture: user.facebookPagePicture || user.facebookPicture,
      },
    });
  } catch (error) {
    console.error("Connect Facebook error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to connect Facebook",
      error: error.message,
    });
  }
};