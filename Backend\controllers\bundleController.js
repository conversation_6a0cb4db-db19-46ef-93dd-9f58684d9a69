const Bundle = require("../models/bundle");
const TeamMember = require("../models/teamMember");
const Video = require("../models/video");
const fs = require("fs");
const { getVideoMetadata } = require("../utils/videoUtils");
// Firebase removed - using Appwrite for storage
const { permission } = require("process");

// Helper to check if user is owner or team member with permission
async function hasBundlePermission(userId, permission = "manageBundles") {
  // Owner always has permission
  const teamMembership = await TeamMember.findOne({
    user: userId,
    status: "active",
  });
  if (!teamMembership) return true;
  // permissions is an object, not an array
  return !!(
    teamMembership.permissions && teamMembership.permissions[permission]
  );
}

// Get all bundles for a user (owner or team member)
exports.getBundles = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      // permissions is now an array, so check with includes
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("view")
      ) {
        return res.status(403).json({
          message: "You do not have permission to view this owner's bundles.",
          permission: false,
        });
      }
    }
    const bundles = await Bundle.find({ userId: ownerId });
    res.status(200).json(bundles);
  } catch (error) {
    next(error);
  }
};

// Get a single bundle
exports.getBundle = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("view")
      ) {
        return res.status(403).json({
          message: "You do not have permission to view this owner's bundles.",
          permission: false,
        });
      }
    }
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    res.status(200).json(bundle);
  } catch (error) {
    next(error);
  }
};

// Create a new bundle
exports.createBundle = async (req, res, next) => {
  try {
    // Only allow if user is owner or has permission
    if (!(await hasBundlePermission(req.user.id, "manageBundles"))) {
      return res
        .status(403)
        .json({ message: "No permission to create bundles" });
    }

    const { name, description, videos } = req.body;

    if (req.user.id) {
      console.log(`User ID: ${req.user.id}`);
    } else {
      console.log("User ID is not available");
    }
    if (!name) {
      return res.status(400).json({ message: "Bundle name is required" });
    }
    // Convert videos to proper format
    const formattedVideos = Array.isArray(videos)
      ? videos.map((v) => ({
          videoId: v.videoId || v,
          fileName: v.fileName || null,
          thumbnailUrl: v.thumbnailUrl || null,
          status: v.status || "pending",
          order: typeof v.order === "number" ? v.order : 0,
        }))
      : [
          {
            videoId: videos,
            fileName: null,
            status: "pending",
            order: 0,
          },
        ];

    // Sort videos by order to ensure correct sequence
    const sortedVideos = [...formattedVideos].sort((a, b) => a.order - b.order);

    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("upload")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to create bundles for this owner.",
          permission: false,
        });
      }
    }
    const bundle = new Bundle({
      name,
      description,
      userId: ownerId,
      videos: sortedVideos,
    });

    await bundle.save();

    res.status(201).json({
      success: true,
      message: "Bundle created successfully",
      bundleId: bundle._id,
    });
  } catch (error) {
    next(error);
  }
};

// Update a bundle
exports.updateBundle = async (req, res, next) => {
  try {
    // Only allow if user is owner or has permission
    if (!(await hasBundlePermission(req.user.id, "manageBundles"))) {
      return res
        .status(403)
        .json({ message: "No permission to update bundles" });
    }

    const { name, description, videos } = req.body;

    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("edit")
      ) {
        return res.status(403).json({
          message: "You do not have permission to update this owner's bundles.",
          permission: false,
        });
      }
    }
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Update fields
    if (name) bundle.name = name;
    if (description) bundle.description = description;

    // Update videos if provided
    if (videos) {
      // Validate videos structure
      if (!Array.isArray(videos)) {
        return res.status(400).json({ message: "Videos must be an array" });
      }

      // Create a map of existing videos by videoId
      const existingVideosMap = new Map();
      bundle.videos.forEach((video) => {
        existingVideosMap.set(video.videoId.toString(), video);
      });

      // Create updated videos array preserving metadata
      const updatedVideos = videos.map((video) => {
        const existingVideo = existingVideosMap.get(video.videoId);
        if (existingVideo) {
          return {
            ...existingVideo.toObject(),
            order: video.order,
          };
        }
        return video; // Fallback if not found
      });

      bundle.videos = updatedVideos;
    }

    await bundle.save();

    res.status(200).json(bundle);
  } catch (error) {
    next(error);
  }
};

// Delete a bundle
exports.deleteBundle = async (req, res, next) => {
  try {
    // Only allow if user is owner or has permission
    if (!(await hasBundlePermission(req.user.id, "manageBundles"))) {
      return res
        .status(403)
        .json({ message: "No permission to delete bundles" });
    }

    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("delete")
      ) {
        return res.status(403).json({
          message: "You do not have permission to delete this owner's bundles.",
          permission: false,
        });
      }
    }
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    await Bundle.deleteOne({ _id: bundle._id });

    res.status(200).json({ message: "Bundle deleted successfully" });
  } catch (error) {
    next(error);
  }
};

// Delete Video from bundle
exports.deleteBundleVideo = async (req, res, next) => {
  try {
    const { videoId } = req.params;
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("delete")
      ) {
        return res.status(403).json({
          message: "You do not have permission to delete this owner's bundles.",
          permission: false,
        });
      }
    }

    // Check if bundle exists and belongs to user
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Check if video exists in bundle
    const videoIndex = bundle.videos.findIndex(
      (v) => v.videoId.toString() === videoId
    );

    if (videoIndex === -1) {
      return res.status(404).json({ message: "Video not found in bundle" });
    }

    // Remove video from bundle
    bundle.videos.splice(videoIndex, 1);

    // Reorder remaining videos
    bundle.videos.forEach((video, index) => {
      video.order = index;
    });

    // Update video document to remove bundleId
    await Video.findByIdAndUpdate(videoId, { $unset: { bundleId: 1 } });

    await bundle.save();

    res.status(200).json({
      success: true,
      message: "Video removed from bundle successfully",
    });
  } catch (error) {
    next(error);
  }
};

// Add video to bundle
exports.addVideoToBundle = async (req, res, next) => {
  try {
    const { videoId } = req.body; // refers to main Video document id
    const ownerId = req.query.ownerId || req.user.id;
    if (!videoId) {
      return res.status(400).json({ message: "Video ID is required" });
    }
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("manageBundles")
      ) {
        return res.status(403).json({
          message: "You do not have permission to manage this owner's bundles.",
          permission: false,
        });
      }
    }

    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });
    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    const video = await Video.findOne({ _id: videoId, userId: req.user.id });
    if (!video) {
      return res.status(404).json({ message: "Video not found" });
    }

    // Extract subdocument IDs
    const videoUrlIds = video.videoUrls.map((u) => u._id);

    // Prevent duplicates of any URL
    const duplicate = bundle.videos.some((entry) => {
      const existingIds = Array.isArray(entry.videoId)
        ? entry.videoId
        : [entry.videoId];
      return existingIds.some((id) =>
        videoUrlIds.map((vId) => vId.toString()).includes(id.toString())
      );
    });
    if (duplicate) {
      return res.status(400).json({ message: "Some URLs already in bundle" });
    }

    // Determine order
    const order = bundle.videos.length
      ? Math.max(...bundle.videos.map((v) => v.order)) + 1
      : 0;

    // Define allowed statuses based on schema enum
    const allowedStatuses = [
      "uploaded",
      "pending",
      "processing",
      "failed",
      "ready",
    ];
    const status = allowedStatuses.includes(video.status)
      ? video.status
      : "pending";

    // Push subdoc with all required fields
    bundle.videos.push({
      videoId: videoUrlIds, // array of videoUrls._id
      fileName: video.videoUrls[0]?.fileName || null,
      status,
      order,
    });

    await bundle.save();

    // Optionally link video to bundle
    video.bundleId = bundle._id;
    await video.save();

    res
      .status(200)
      .json({ message: "Video URLs added to bundle", bundleId: bundle._id });
  } catch (error) {
    next(error);
  }
};

// Remove video from bundle
exports.removeVideoFromBundle = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("delete")
      ) {
        return res.status(403).json({
          message: "You do not have permission to delete this owner's bundles.",
          permission: false,
        });
      }
    }
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Remove video from bundle
    bundle.videos = bundle.videos.filter(
      (v) => v.videoId.toString() !== req.params.videoId
    );

    // Reorder remaining videos
    bundle.videos.forEach((video, index) => {
      video.order = index;
    });

    await bundle.save();

    res.status(200).json(bundle);
  } catch (error) {
    next(error);
  }
};

// Reorder videos in bundle
exports.reorderVideos = async (req, res, next) => {
  try {
    const { videoOrders } = req.body;
    const ownerId = req.query.ownerId || req.user.id;

    if (!Array.isArray(videoOrders)) {
      return res.status(400).json({ message: "videoOrders must be an array" });
    }

    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Update order of each video
    videoOrders.forEach((item) => {
      const video = bundle.videos.find(
        (v) => v.videoId.toString() === item.videoId
      );
      if (video) {
        video.order = item.order;
      }
    });

    // Sort videos by order
    bundle.videos.sort((a, b) => a.order - b.order);

    await bundle.save();

    res.status(200).json(bundle);
  } catch (error) {
    next(error);
  }
};

// Add multiple videos to bundle
exports.addMultipleVideosToBundle = async (req, res, next) => {
  try {
    const { videoIds } = req.body;
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("manageBundles")
      ) {
        return res.status(403).json({
          message: "You do not have permission to manage this owner's bundles.",
          permission: false,
        });
      }
    }

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(400).json({ message: "Video IDs array is required" });
    }

    // Check if bundle exists and belongs to user
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Check if videos exist and belong to user
    const videos = await Video.find({
      _id: { $in: videoIds },
      userId: req.user.id,
    });

    if (videos.length !== videoIds.length) {
      return res.status(404).json({
        message: "One or more videos not found or do not belong to this user",
      });
    }

    // Get current max order
    const maxOrder =
      bundle.videos.length > 0
        ? Math.max(...bundle.videos.map((v) => v.order))
        : -1;

    // Add videos to bundle
    const addedVideos = [];

    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      const videoId = video._id.toString();

      // Check if video is already in bundle
      const videoExists = bundle.videos.some(
        (v) => v.videoId.toString() === videoId
      );

      if (!videoExists) {
        // Add to bundle
        const order = maxOrder + i + 1;

        bundle.videos.push({
          videoId,
          order,
        });

        // Update video with bundleId
        video.bundleId = bundle._id;
        await video.save();

        addedVideos.push(video);

        // Firebase storage removed - using Appwrite
      }
    }

    // Firebase storage removed - using Appwrite for file storage

    await bundle.save();

    res.status(200).json({
      message: `${addedVideos.length} videos added to bundle`,
      bundle,
      addedVideos,
    });
  } catch (error) {
    next(error);
  }
};

// Upload multiple videos to a bundle and save to Firebase
exports.uploadVideosToBundle = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("upload")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to upload videos to this owner's bundles.",
          permission: false,
        });
      }
    }
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: "No files uploaded" });
    }

    // Check if bundle exists
    const bundle = await Bundle.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!bundle) {
      return res.status(404).json({ message: "Bundle not found" });
    }

    // Process each uploaded file
    const uploadedVideos = [];

    for (const file of req.files) {
      try {
        const filePath = file.path;
        const fileSize = file.size;
        const metadata = await getVideoMetadata(filePath);

        // Create new video
        const video = new Video({
          title: file.originalname.replace(/\.[^/.]+$/, "") || "Untitled Video",
          description: "",
          filePath: filePath,
          fileSize: fileSize,
          duration: metadata.duration,
          thumbnailPath: metadata.thumbnailPath,
          status: "ready",
          userId: req.user.id,
          bundleId: bundle._id,
        });

        await video.save();

        // Add to bundle
        const order =
          bundle.videos.length > 0
            ? Math.max(...bundle.videos.map((v) => v.order)) + 1
            : 0;

        bundle.videos.push({
          videoId: video._id,
          order,
        });

        uploadedVideos.push(video);

        // Firebase storage removed - using Appwrite
      } catch (error) {
        // Clean up file if there's an error
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
        console.error(`Error processing file ${file.originalname}:`, error);
      }
    }

    // Firebase storage removed - using Appwrite for file storage

    await bundle.save();

    res.status(200).json({
      message: `${uploadedVideos.length} videos uploaded to bundle`,
      bundle,
      videos: uploadedVideos,
    });
  } catch (error) {
    // Clean up all files if there's an error
    if (req.files) {
      for (const file of req.files) {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }
    next(error);
  }
};
