const Content = require("../models/content");

// Create new content
exports.createContent = async (req, res) => {
  try {
    const { imageUrl, title, description, downloadUrl } = req.body;

    if (!imageUrl || !title || !description) {
      return res.status(400).json({
        success: false,
        message: "Image URL, title, and description are required",
      });
    }

    if (!req.user || !req.user._id) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const content = new Content({
      imageUrl,
      title,
      description,
      downloadUrl,
      createdBy: req.user._id,
    });


    await content.save();

    res.status(201).json({
      success: true,
      message: "Content created successfully",
      data: content,
    });
  } catch (error) {
    console.error("Error creating content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get all content
exports.getAllContent = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const skip = (page - 1) * limit;

    let query = {};
    if (status) {
      query.status = status;
    }

    // For public access, only show active content
    if (!req.user) {
      query.status = 'active';
    }

    // Add filter to only include content with valid download URLs
    query.downloadUrl = { 
      $exists: true, 
      $ne: null, 
      $ne: '', 
      $regex: /^(https?:\/\/|drive\.google\.com|dropbox\.com|onedrive\.live\.com)/i 
    };

    const content = await Content.find(query)
      .populate("createdBy", "name email")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Filter out content with broken or inaccessible download links
    const validContent = content.filter(item => {
      if (!item.downloadUrl) return false;
      
      // Check if it's a valid URL format
      try {
        const url = new URL(item.downloadUrl);
        return url.protocol === 'http:' || url.protocol === 'https:';
      } catch {
        return false;
      }
    });

    const total = await Content.countDocuments(query);

    res.status(200).json({
      success: true,
      data: validContent,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("Error fetching content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get content by ID
exports.getContentById = async (req, res) => {
  try {
    const { id } = req.params;

    const content = await Content.findById(id).populate("createdBy", "name email");

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (error) {
    console.error("Error fetching content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Update content
exports.updateContent = async (req, res) => {
  try {
    const { id } = req.params;
    const { imageUrl, title, description, downloadUrl, status } = req.body;

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    // Update fields if provided
    if (imageUrl !== undefined) content.imageUrl = imageUrl;
    if (title !== undefined) content.title = title;
    if (description !== undefined) content.description = description;
    if (downloadUrl !== undefined) content.downloadUrl = downloadUrl;
    if (status !== undefined) content.status = status;

    await content.save();

    res.status(200).json({
      success: true,
      message: "Content updated successfully",
      data: content,
    });
  } catch (error) {
    console.error("Error updating content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Delete content
exports.deleteContent = async (req, res) => {
  try {
    const { id } = req.params;

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    await Content.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Content deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Update content status
exports.updateContentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status || !["active", "inactive", "draft"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Valid status is required (active, inactive, draft)",
      });
    }

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    content.status = status;
    await content.save();

    res.status(200).json({
      success: true,
      message: "Content status updated successfully",
      data: content,
    });
  } catch (error) {
    console.error("Error updating content status:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Like content
exports.likeContent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    // Check if user already liked the content
    const alreadyLiked = content.likes.includes(userId);

    if (alreadyLiked) {
      return res.status(400).json({
        success: false,
        message: "Content already liked",
      });
    }

    // Add user to likes array
    content.likes.push(userId);
    await content.save();

    res.status(200).json({
      success: true,
      message: "Content liked successfully",
      data: {
        contentId: content._id,
        likesCount: content.likes.length,
        isLiked: true
      },
    });
  } catch (error) {
    console.error("Error liking content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Unlike content
exports.unlikeContent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    // Check if user has liked the content
    const hasLiked = content.likes.includes(userId);

    if (!hasLiked) {
      return res.status(400).json({
        success: false,
        message: "Content not liked",
      });
    }

    // Remove user from likes array
    content.likes = content.likes.filter(likeId => !likeId.equals(userId));
    await content.save();

    res.status(200).json({
      success: true,
      message: "Content unliked successfully",
      data: {
        contentId: content._id,
        likesCount: content.likes.length,
        isLiked: false
      },
    });
  } catch (error) {
    console.error("Error unliking content:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Toggle like (like if not liked, unlike if already liked)
exports.toggleLike = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const content = await Content.findById(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: "Content not found",
      });
    }

    const hasLiked = content.likes.includes(userId);

    if (hasLiked) {
      // Unlike
      content.likes = content.likes.filter(likeId => !likeId.equals(userId));
    } else {
      // Like
      content.likes.push(userId);
    }

    await content.save();

    res.status(200).json({
      success: true,
      message: hasLiked ? "Content unliked successfully" : "Content liked successfully",
      data: {
        contentId: content._id,
        likesCount: content.likes.length,
        isLiked: !hasLiked
      },
    });
  } catch (error) {
    console.error("Error toggling like:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}; 