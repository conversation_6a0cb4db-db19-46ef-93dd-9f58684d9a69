const axios = require('axios');
const User = require('../models/user');

const FACEBOOK_APP_ID = process.env.FACEBOOK_APP_ID;
const FACEBOOK_APP_SECRET = process.env.FACEBOOK_APP_SECRET;
const FACEBOOK_REDIRECT_URI = process.env.FACEBOOK_REDIRECT_URI || 'http://localhost:8000/api/facebook/callback';

// 1. Start Facebook OAuth flow
exports.startFacebookAuth = (req, res) => {
  const state = req.user ? req.user._id : '';
  const authUrl = `https://www.facebook.com/v19.0/dialog/oauth?client_id=${FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent(FACEBOOK_REDIRECT_URI)}&scope=pages_show_list,pages_read_engagement,pages_manage_posts,pages_read_user_content,publish_video&response_type=code&state=${state}`;
  res.redirect(authUrl);
};

// 2. Handle Facebook OAuth callback
exports.facebookAuthCallback = async (req, res) => {
  try {
    const { code, state } = req.query;
    if (!code) return res.status(400).send('Missing code');

    // Exchange code for access token
    const tokenRes = await axios.get('https://graph.facebook.com/v19.0/oauth/access_token', {
      params: {
        client_id: FACEBOOK_APP_ID,
        client_secret: FACEBOOK_APP_SECRET,
        redirect_uri: FACEBOOK_REDIRECT_URI,
        code,
      },
    });
    const accessToken = tokenRes.data.access_token;

    // Get user info
    const userRes = await axios.get('https://graph.facebook.com/me', {
      params: { access_token: accessToken, fields: 'id,name,picture' },
    });
    const facebookUserId = userRes.data.id;
    const facebookName = userRes.data.name;
    const facebookPicture = userRes.data.picture?.data?.url;

    // Get pages
    const pagesRes = await axios.get(`https://graph.facebook.com/${facebookUserId}/accounts`, {
      params: { access_token: accessToken },
    });
    const page = pagesRes.data.data && pagesRes.data.data[0];
    if (!page) return res.status(400).send('No Facebook page found. Please create a page.');

    // Save tokens and page info to user
    const user = await User.findById(state);
    if (!user) return res.status(404).send('User not found');
    user.facebookConnected = true;
    user.facebookAccessToken = accessToken;
    user.facebookUserId = facebookUserId;
    user.facebookName = facebookName;
    user.facebookPicture = facebookPicture;
    user.facebookPageId = page.id;
    user.facebookPageName = page.name;
    user.facebookPageAccessToken = page.access_token;
    user.facebookPagePicture = page.picture?.data?.url;
    await user.save();

    // Redirect to frontend (customize as needed)
    res.redirect('/profile?facebook=connected');
  } catch (err) {
    console.error('Facebook OAuth error:', err.response?.data || err.message);
    res.status(500).send('Facebook authentication failed');
  }
};

// 3. Upload video to Facebook
exports.uploadVideoToFacebook = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    if (!user || !user.facebookConnected || !user.facebookPageAccessToken || !user.facebookPageId) {
      return res.status(400).json({ success: false, message: 'Facebook not connected' });
    }
    const { videoUrl, title, description } = req.body;
    if (!videoUrl) return res.status(400).json({ success: false, message: 'Missing video URL' });

    // Upload video to Facebook page
    const fbRes = await axios.post(`https://graph-video.facebook.com/v19.0/${user.facebookPageId}/videos`, null, {
      params: {
        file_url: videoUrl,
        title,
        description,
        access_token: user.facebookPageAccessToken,
      },
    });
    res.json({ success: true, facebook: fbRes.data });
  } catch (err) {
    console.error('Facebook video upload error:', err.response?.data || err.message);
    res.status(500).json({ success: false, message: 'Failed to upload video to Facebook', error: err.response?.data || err.message });
  }
}; 