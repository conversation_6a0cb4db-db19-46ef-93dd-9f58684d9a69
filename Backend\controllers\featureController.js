const User = require('../models/user');
const TeamMember = require('../models/teamMember'); // Add this import

/**
 * Get user data with subscription-based filtering
 */
exports.getUserData = async (req, res) => {
  try {
    // Check if subscriptionData is present
    if (!req.subscriptionData) {
      return res.status(400).json({
        success: false,
        message: "Subscription data not found. Please ensure subscription middleware is applied.",
      });
    }
    const { plan, features, isFree } = req.subscriptionData;
    
    // Determine the owner user (if current user is a team member, get the owner)
    let userId = req.user.id;
    const teamMembership = await TeamMember.findOne({ user: userId, status: "active" });
    if (teamMembership) {
      userId = teamMembership.owner;
    }

    // Get the user with only the fields they should have access to
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Create a filtered user object based on subscription
    const filteredUser = {
      _id: user._id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      createdAt: user.createdAt,
      subscription: {
        planName: plan.name,
        status: req.subscriptionData.status,
        features: features.map(f => ({ key: f.key, name: f.name }))
      }
    };
    
    // Add additional fields based on subscription level
    if (!isFree) {
      // Add fields only available to paid subscribers
      filteredUser.youtubeConnected = user.youtubeConnected;
      filteredUser.lastLogin = user.lastLogin;
      
      // Add premium fields for specific plans
      const hasPremiumAccess = features.some(f => f.key === 'premium_analytics');
      if (hasPremiumAccess) {
        filteredUser.analytics = user.analytics;
        filteredUser.advancedStats = user.advancedStats;
      }
    }
    
    res.status(200).json({
      success: true,
      user: filteredUser
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user data',
      error: error.message
    });
  }
};

/**
 * Get feature-specific data
 */
exports.getFeatureData = async (req, res) => {
  try {
    const { featureKey } = req.params;
    const { features } = req.subscriptionData;
    
    // Find the specific feature in the user's plan
    const feature = features.find(f => f.key === featureKey);
    
    if (!feature) {
      return res.status(403).json({
        success: false,
        message: 'This feature is not available in your current plan',
        upgradeRequired: true
      });
    }
    
    // Return feature-specific data and limits
    res.status(200).json({
      success: true,
      feature: {
        key: feature.key,
        name: feature.name,
        description: feature.description,
        value: feature.value,
        limit: feature.limit
      },
      planName: req.subscriptionData.plan.name
    });
  } catch (error) {
    console.error('Error fetching feature data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch feature data',
      error: error.message
    });
  }
};