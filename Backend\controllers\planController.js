const Plan = require('../models/plan');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Initialize default plans
exports.initializePlans = async () => {
  try {
    const plansCount = await Plan.countDocuments();
    
    if (plansCount === 0) {
      console.log('Creating default subscription plans...');
      
      // Create plans in Stripe first
      const freePlan = await createStripePlan('Free Plan', 'Basic YouTube scheduling', 0, 0);
      const creatorPlan = await createStripePlan('Creator Plan', 'For growing creators', 999, 9900);
      const proPlan = await createStripePlan('Professional Plan', 'For serious content creators', 1999, 19900);
      
      // Create plans in database
      await Plan.create([
        {
          name: 'Free',
          monthlyPrice: 0,
          yearlyPrice: 0,
          stripeProductId: freePlan.productId,
          monthlyPriceId: freePlan.monthlyPriceId,
          yearlyPriceId: freePlan.yearlyPriceId,
          features: {
            videoUploadsLimit: 5,
            videoStorageDays: 7,
            storageLimit: 250, // 250MB storage limit
            bundleLimit: 2,
            scheduleLimit: 1,
            schedulingOptions: ['daily'],
            prioritySupport: false,
            bulkUpload: false,
            teamMembers: 1 // Only owner, no invites allowed
          }
        },
        {
          name: 'Creator',
          monthlyPrice: 9.99,
          yearlyPrice: 99,
          stripeProductId: creatorPlan.productId,
          monthlyPriceId: creatorPlan.monthlyPriceId,
          yearlyPriceId: creatorPlan.yearlyPriceId,
          features: {
            videoUploadsLimit: 30,
            videoStorageDays: 30,
            storageLimit: 5000, // 5GB storage limit
            bundleLimit: 10,
            scheduleLimit: 5,
            schedulingOptions: ['daily', 'weekly', 'monthly'],
            prioritySupport: true,
            bulkUpload: false,
            teamMembers: 3
          }
        },
        {
          name: 'Professional',
          monthlyPrice: 19.99,
          yearlyPrice: 199,
          stripeProductId: proPlan.productId,
          monthlyPriceId: proPlan.monthlyPriceId,
          yearlyPriceId: proPlan.yearlyPriceId,
          features: {
            videoUploadsLimit: -1, // Unlimited
            videoStorageDays: 90,
            storageLimit: 50000, // 50GB storage limit
            bundleLimit: -1, // Unlimited
            scheduleLimit: -1, // Unlimited
            schedulingOptions: ['daily', 'weekly', 'monthly'],
            prioritySupport: true,
            bulkUpload: true,
            teamMembers: 10
          }
        }
      ]);
      
      console.log('Default plans created successfully');
    }
  } catch (error) {
    console.error('Error initializing plans:', error);
  }
};

// Helper function to create plans in Stripe
async function createStripePlan(name, description, monthlyAmount, yearlyAmount) {
  try {
    // Create product
    const product = await stripe.products.create({
      name,
      description
    });
    
    // Create monthly price
    const monthlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: monthlyAmount,
      currency: 'usd',
      recurring: { 
        interval: 'month' 
      }
    });
    
    // Create yearly price
    const yearlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: yearlyAmount,
      currency: 'usd',
      recurring: { 
        interval: 'year' 
      }
    });
    
    return {
      productId: product.id,
      monthlyPriceId: monthlyPrice.id,
      yearlyPriceId: yearlyPrice.id
    };
  } catch (error) {
    console.error(`Error creating Stripe plan ${name}:`, error);
    throw error;
  }
}

// Get all plans
exports.getPlans = async (req, res) => {
  try {
    const plans = await Plan.find({ isActive: true }).populate('features');
    if(!plans) {
      return res.status(404).json({ message: 'Plans not found' });
    }
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching plans', error: error.message });
  }
};

