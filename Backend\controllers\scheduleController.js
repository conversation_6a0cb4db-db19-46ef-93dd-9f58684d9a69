const Schedule = require("../models/schedule");
const Bundle = require("../models/bundle");
const User = require("../models/user");
const Video = require("../models/video");
const {
  calculateNextRunTime,
  generateCronExpression,
  scheduleYoutubeUploadJob,
} = require("../utils/scheduleUtils");
const { runYoutubeUpload } = require("../utils/youtubeUtils");
const tokenManager = require("../utils/tokenManager");
const teamMember = require("../models/teamMember");
const sendEmailController = require("./sendEmailController");

// Get all schedules for a user
exports.getSchedules = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await teamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("view")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to upload videos for this owner.",
          permission: false,
        });
      }
    }
    const schedules = await Schedule.find({ userId: ownerId }).populate(
      "bundleId"
    );

    res.status(200).json(schedules);
  } catch (error) {
    next(error);
  }
};

// Get a single schedule
exports.getSchedule = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await teamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("view")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to view schedules for this owner.",
          permission: false,
        });
      }
    }
    const schedule = await Schedule.findOne({
      _id: req.params.id,
      userId: ownerId,
    }).populate("bundleId");

    if (!schedule) {
      return res.status(404).json({ message: "Schedule not found" });
    }

    res.status(200).json(schedule);
  } catch (error) {
    next(error);
  }
};

// Create a new schedule
exports.createSchedule = async (req, res, next) => {
  try {
    const {
      name,
      description,
      bundleId,
      startDate,
      timezone = "UTC",
      frequency = "daily",
      daysOfWeek,
      dayOfMonth,
      timeOfDay,
    } = req.body;
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await teamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("upload")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to upload schedule for this owner.",
          permission: false,
        });
      }
    }

    // Validate required fields
    if (!name || !bundleId || !startDate || !timeOfDay) {
      return res.status(400).json({
        message:
          "Missing required fields: name, bundleId, startDate, timeOfDay",
      });
    }

    // Check if bundle exists and belongs to user
    const bundle = await Bundle.findOne({
      _id: bundleId,
      userId: ownerId,
    });

    if (!bundle) {
      return res
        .status(404)
        .json({ message: "Bundle not found or unauthorized" });
    }

    // Check YouTube connection
    const user = await User.findById(req.ownerId || req.user.id);
    if (!user?.youtubeConnected) {
      return res.status(400).json({
        message: "YouTube account not connected",
        youtubeConnected: false,
      });
    }

    // Validate frequency-specific fields
    if (
      frequency === "weekly" &&
      (!daysOfWeek || (Array.isArray(daysOfWeek) && daysOfWeek.length === 0))
    ) {
      return res.status(400).json({
        message: "Days of week are required for weekly frequency",
      });
    }

    if (frequency === "monthly" && !dayOfMonth) {
      return res.status(400).json({
        message: "Day of month is required for monthly frequency",
      });
    }

    // Calculate next run time and cron expression
    const scheduleData = {
      frequency,
      daysOfWeek: Array.isArray(daysOfWeek) ? daysOfWeek : [daysOfWeek],
      dayOfMonth,
      timeOfDay,
      timezone,
      startDate: new Date(startDate),
    };

    const nextRunAt = calculateNextRunTime(scheduleData);

    // Generate cron expression
    const cronExpression = generateCronExpression({
      frequency: scheduleData.frequency,
      daysOfWeek: scheduleData.daysOfWeek,
      dayOfMonth: scheduleData.dayOfMonth,
      timeOfDay: scheduleData.timeOfDay,
    });

    // Create the schedule
    const schedule = new Schedule({
      name,
      description,
      bundleId,
      startDate: scheduleData.startDate,
      timezone: timezone || user.timezone || 'UTC',
      frequency,
      daysOfWeek: scheduleData.daysOfWeek,
      dayOfMonth,
      timeOfDay,
      cronExpression,
      nextRunAt,
      status: "active",
      userId: req.ownerId || req.user.id,
    });

    await schedule.save();
    console.log("Schedule saved:", schedule);

    // Schedule the job if not a one-time schedule
    if (frequency !== "once") {
      await scheduleYoutubeUploadJob(schedule, bundle);
    } else {
      // For one-time schedules, queue immediately if start time is in past
      if (new Date(startDate) <= new Date()) {
        await queueImmediateUpload(schedule, bundle);
      }
    }

    res.status(201).json(schedule);
  } catch (error) {
    console.error("Error creating schedule:", error);
    next(error);
  }
};

// Update a schedule
exports.updateSchedule = async (req, res, next) => {
  try {
    const {
      name,
      description,
      bundleId,
      startDate,
      timezone,
      frequency,
      daysOfWeek,
      timeOfDay,
      dayOfMonth,
      status,
    } = req.body;
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await teamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("edit")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to edit schedule for this owner.",
          permission: false,
        });
      }
    }

    const schedule = await Schedule.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!schedule) {
      return res.status(404).json({ message: "Schedule not found" });
    }

    // If bundleId is provided, check if it exists and belongs to user
    if (bundleId) {
      const bundle = await Bundle.findOne({
        _id: bundleId,
        userId: req.ownerId || req.user.id,
      });

      if (!bundle) {
        return res.status(404).json({ message: "Bundle not found" });
      }

      schedule.bundleId = bundleId;
    }

    // Update fields
    if (name) schedule.name = name;
    if (description !== undefined) schedule.description = description;
    if (startDate) schedule.startDate = new Date(startDate);
    if (timezone) schedule.timezone = timezone;
    if (frequency) schedule.frequency = frequency;
    if (daysOfWeek) schedule.daysOfWeek = daysOfWeek;
    if (timeOfDay) schedule.timeOfDay = timeOfDay;
    if (dayOfMonth) schedule.dayOfMonth = dayOfMonth;
    if (status) schedule.status = status;

    // Recalculate next run time if scheduling parameters changed
    if (startDate || timezone || frequency || daysOfWeek || timeOfDay) {
      schedule.nextRunAt = calculateNextRunTime({
        startDate: schedule.startDate,
        timezone: schedule.timezone,
        frequency: schedule.frequency,
        daysOfWeek: schedule.daysOfWeek,
        timeOfDay: schedule.timeOfDay,
      });
    }

    await schedule.save();

    res.status(200).json(schedule);
  } catch (error) {
    next(error);
  }
};

// Delete a schedule
exports.deleteSchedule = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await teamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });
      if (
        !teamMembership ||
        !Array.isArray(teamMembership.permissions) ||
        !teamMembership.permissions.includes("delete")
      ) {
        return res.status(403).json({
          message:
            "You do not have permission to delete schedule for this owner.",
          permission: false,
        });
      }
    }

    const schedule = await Schedule.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!schedule) {
      return res.status(404).json({ message: "Schedule not found" });
    }

    await Schedule.deleteOne({ _id: schedule._id });

    res.status(200).json({ message: "Schedule deleted successfully" });
  } catch (error) {
    next(error);
  }
};

// Pause a schedule
exports.pauseSchedule = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;

    const schedule = await Schedule.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!schedule) {
      return res.status(404).json({ message: "Schedule not found" });
    }

    schedule.status = "paused";
    await schedule.save();

    res.status(200).json(schedule);
  } catch (error) {
    next(error);
  }
};

// Resume a schedule
exports.resumeSchedule = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId || req.user.id;

    const schedule = await Schedule.findOne({
      _id: req.params.id,
      userId: ownerId,
    });

    if (!schedule) {
      return res.status(404).json({ message: "Schedule not found" });
    }

    schedule.status = "active";

    // Recalculate next run time
    schedule.nextRunAt = calculateNextRunTime({
      startDate: schedule.startDate,
      timezone: schedule.timezone,
      frequency: schedule.frequency,
      daysOfWeek: schedule.daysOfWeek,
      timeOfDay: schedule.timeOfDay,
    });

    await schedule.save();

    res.status(200).json(schedule);
  } catch (error) {
    next(error);
  }
};

// Process a scheduled upload
exports.processScheduledUpload = async (scheduleId) => {
  try {
    console.log(`Processing scheduled upload for schedule ${scheduleId}`);

    // Find the schedule
    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      console.log(`Schedule ${scheduleId} not found`);
      return;
    }

    // Check if schedule is active
    if (schedule.status !== "active") {
      console.log(
        `Schedule ${scheduleId} is not active (status: ${schedule.status})`
      );
      return;
    }

    // Find the bundle
    const bundle = await Bundle.findById(schedule.bundleId);
    if (!bundle) {
      console.log(`Bundle ${schedule.bundleId} not found`);
      return;
    }

    // Get the next video to upload
    const nextVideoIndex = schedule.currentVideoIndex || 0;
    if (nextVideoIndex >= bundle.videos.length) {
      // We've uploaded all videos, start over or mark as completed
      if (schedule.frequency === "once") {
        schedule.status = "completed";
        await schedule.save();
        console.log(`One-time schedule ${scheduleId} completed`);
        return;
      }

      // For recurring schedules, start over
      schedule.currentVideoIndex = 0;
      await schedule.save();
      console.log(`Reset video index for recurring schedule ${scheduleId}`);
      return;
    }

    const videoEntry = bundle.videos[nextVideoIndex];
    const video = await Video.findById(videoEntry.videoId);

    if (!video) {
      console.log(`Video ${videoEntry.videoId} not found`);
      schedule.currentVideoIndex = nextVideoIndex + 1;
      await schedule.save();
      return;
    }

    console.log(`Processing video: ${video.title}`);

    // Get authenticated client
    const { getAuthClient } = require("../utils/tokenManager");
    const oauth2Client = await getAuthClient(schedule.userId);

    // Get the file content
    const { getFileContent } = require("../utils/appwriteDirectUpload");
    const videoBuffer = await getFileContent(video.appwriteFileId);

    // Upload to YouTube
    const { runYoutubeUpload } = require("../utils/youtubeUtils");
    const platform = schedule.platform || "youtube"; // Add platform to schedule model

    let uploadResult;
    if (platform === "youtube") {
      uploadResult = await runYoutubeUpload({
        oauth2Client,
        videoBuffer,
        title: video.title,
        description: video.description,
        tags: videoEntry.tags || [],
        privacyStatus: videoEntry.privacyStatus || "private",
      });
    } else if (platform === "instagram") {
      // Call your Instagram upload logic here
    } else if (platform === "facebook") {
      // Call your Facebook upload logic here
    }

    if (uploadResult.success) {
      // Update video status
      video.status = "uploaded";
      video.videoUrls.push({
        url: uploadResult.videoUrl,
        isUploaded: true,
        platform: "youtube",
        uploadDate: new Date(),
        fileName: video.title,
        fileId: uploadResult.videoId,
      });

      await video.save();

      // Update schedule
      schedule.currentVideoIndex = nextVideoIndex + 1;
      schedule.lastRunAt = new Date();
      schedule.nextRunAt = calculateNextRunTime(schedule.cronExpression);

      // Add to run history if the field exists
      if (Array.isArray(schedule.runHistory)) {
        schedule.runHistory.push({
          date: new Date(),
          status: "success",
          videoId: video._id,
          youtubeVideoId: uploadResult.videoId,
        });
      }

      await schedule.save();

      console.log(`Successfully uploaded video ${video.title} to YouTube`);

      // Send success email notification
      const user = await User.findById(schedule.userId);
      if (user && user.email) {
        sendEmailController.emailWorker("ScheduleSuccessEmail", async () => {
          await sendEmailController.transporter.sendMail({
            from: process.env.EMAIL_HOST_USER,
            to: [user.email],
            subject: `Scheduled Video Uploaded: ${video.title}`,
            html: `<div style='font-family:sans-serif;padding:24px;'><h2>Video Uploaded Successfully</h2><p>Your scheduled video <b>${video.title}</b> was uploaded to YouTube as planned.</p><p>Schedule: <b>${schedule.name}</b></p><p>Time: ${new Date().toLocaleString()}</p></div>`
          });
        });
      }
    } else {
      console.error(`Failed to upload video to YouTube: ${uploadResult.error}`);

      // Add to run history if the field exists
      if (Array.isArray(schedule.runHistory)) {
        schedule.runHistory.push({
          date: new Date(),
          status: "failed",
          videoId: video._id,
          error: uploadResult.error,
        });
        await schedule.save();
      }

      // Handle auth errors
      if (uploadResult.authRequired) {
        schedule.status = "paused";
        schedule.pauseReason = "YouTube authentication required";
        await schedule.save();
      }

      // Send failure email notification
      if (user && user.email) {
        sendEmailController.emailWorker("ScheduleFailureEmail", async () => {
          await sendEmailController.transporter.sendMail({
            from: process.env.EMAIL_HOST_USER,
            to: [user.email],
            subject: `Scheduled Video Upload Failed: ${video.title}`,
            html: `<div style='font-family:sans-serif;padding:24px;'><h2>Video Upload Failed</h2><p>Your scheduled video <b>${video.title}</b> failed to upload to YouTube.</p><p>Error: ${uploadResult.error}</p><p>Schedule: <b>${schedule.name}</b></p><p>Time: ${new Date().toLocaleString()}</p></div>`
          });
        });
      }
    }
  } catch (error) {
    console.error("Error processing scheduled upload:", error);

    // Handle auth errors by marking schedule as paused
    if (
      error.code === 401 ||
      (error.errors && error.errors.some((e) => e.reason === "authError"))
    ) {
      await Schedule.findByIdAndUpdate(scheduleId, {
        status: "paused",
        pauseReason: "YouTube authentication failed",
      });
    }

    // Try to add to run history if possible
    try {
      const schedule = await Schedule.findById(scheduleId);
      if (schedule && Array.isArray(schedule.runHistory)) {
        schedule.runHistory.push({
          date: new Date(),
          status: "failed",
          error: error.message || "Unknown error occurred",
        });
        await schedule.save();
      }
    } catch (historyError) {
      console.error("Error updating run history:", historyError);
    }
  }
};
