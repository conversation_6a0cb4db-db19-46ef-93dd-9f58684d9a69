const { transporter } = require("../config/nodemailer");
const User = require("../models/user");
const Plan = require("../models/plan");
const crypto = require("crypto");
const dotenv = require("dotenv");
dotenv.config();

// Email worker function
const emailWorker = async (taskName, taskFn, maxRetries = 3, delayMs = 2000) => {
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      console.log(`Starting email task: ${taskName}, attempt ${attempt + 1}`);
      await taskFn();
      console.log(`Completed email task: ${taskName}`);
      return;
    } catch (error) {
      attempt++;
      console.error(`Email worker failed (${taskName}), attempt ${attempt}:`, error);
      if (attempt < maxRetries) {
        await new Promise(res => setTimeout(res, delayMs));
      } else {
        console.error(`Email task ${taskName} failed after ${maxRetries} attempts.`);
      }
    }
  }
};

// WelcomeEmail function to send a welcome email to a new user
exports.WelcomeEmail = async (req, res) => {
  try {
    const { email } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    // FIX: Changed condition to check for non-existence
    const existUser = await User.findOne({ email });
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User does not exist",
      });
    }

    await emailWorker("WelcomeEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Welcome to ReelScheduler!",
          html: `
            <div style="background-color:#ffffff; border-radius:8px; padding:20px; max-width:600px; margin:20px auto; box-shadow:0 2px 8px rgba(0, 0, 0, 0.1); font-family: Arial, sans-serif;">
              <h2 style="color:#444;">Welcome to ReelScheduler!</h2>
              <p>Hello,</p>
              <p>Thank you for registering with ReelScheduler. We're excited to have you on board!</p>
              <p>You can now start scheduling and managing your time more effectively.</p>
              <p>If you have any questions, feel free to reach out to our support team.</p>
              <p>Best regards,<br>The ReelScheduler Team</p>
            </div>
          `,
        });
        console.log(
          "Welcome email sent to:",
          email,
          "Message ID:",
          info.messageId
        );
      } catch (error) {
        console.error("Failed to send welcome email:", error);
      }
    });

    return res.status(202).json({
      success: true,
      message: "Welcome email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    }, 5, 3000);
  } catch (error) {
    console.error("Error processing welcome email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during email processing",
      error: error.message,
    });
  }
};

// ForgotPasswordEmail function to send a password reset email
exports.ForgotPasswordEmail = async (req, res) => {
  try {
    const { email } = req.body;

    // Input validation
    if (
      !email ||
      typeof email !== "string" ||
      !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
        code: "INVALID_EMAIL",
      });
    }

    const existUser = await User.findOne({ email });

    // FIX: Security improvement - don't reveal if user doesn't exist
    if (!existUser) {
      return res.status(200).json({
        success: true,
        message:
          "If this email is registered, you'll receive a password reset link",
      });
    }

    // Generate a reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Save hashed token to user document with expiration
    existUser.resetPasswordToken = hashedToken;
    existUser.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await existUser.save();

    // Create reset URL with unhashed token
    const resetUrl = `${
      process.env.FRONTEND_URL
    }/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

    await emailWorker("ForgotPasswordEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Password Reset Request",
          html: `
            <div style="background-color:#ffffff; border-radius:8px; padding:20px; max-width:600px; margin:20px auto; box-shadow:0 2px 8px rgba(0, 0, 0, 0.1); font-family: Arial, sans-serif;">
              <h2 style="color:#444;">Password Reset Request</h2>
              <p>Hello,</p>
              <p>We received a request to reset your password. If you did not make this request, please ignore this email.</p>
              <p>If you would like to reset your password, please click the link below:</p>
              <p><a href="${resetUrl}" style="display:inline-block; background:#007bff; color:#fff; padding:10px 20px; border-radius:5px; text-decoration:none; margin:10px 0;">Reset Password</a></p>
              <p style="color:#ff0000; font-weight:bold;">This link will expire in 1 hour.</p>
              <p>Best regards,<br>The ReelScheduler Team</p>
            </div>
          `,
        });
        console.log(
          "Forgot password email sent to:",
          email,
          "Message ID:",
          info.messageId
        );
      } catch (error) {
        console.error("Failed to send password reset email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Password reset email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing password reset:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during password reset",
      error: error.message,
    });
  }
};

// resetPasswordEmail function to send a confirmation email after password reset
exports.resetPasswordEmail = async (req, res) => {
  try {
    const { email } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
        code: "INVALID_EMAIL_BRO",
      });
    }

    const existUser = await User.findOne({ email });
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message:
          "If this email is registered, you'll receive a password reset link",
      });
    }

    await emailWorker("ResetPasswordEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Password Reset Confirmation",
          html: `
            <div style="background-color:#ffffff; border-radius:8px; padding:20px; max-width:600px; margin:20px auto; box-shadow:0 2px 8px rgba(0, 0, 0, 0.1); font-family: Arial, sans-serif;">
              <h2 style="color:#444;">Password Reset Confirmation</h2>
              <p>Hello,</p>
              <p>Your password has been successfully reset.</p>
              <p>If you did not request a password reset, please contact our support team immediately.</p>
              <p>Best regards,<br>The ReelScheduler Team</p>
            </div>
          `,
        });
        console.log(
          "Reset confirmation email sent to:",
          email,
          "Message ID:",
          info.messageId
        );
      } catch (error) {
        console.error("Failed to send reset confirmation:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Password reset confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing reset confirmation:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during confirmation",
      error: error.message,
    });
  }
};

// Subscription Created
exports.SubscriptionCompleted = async (req, res) => {
  try {
    const { email } = req.body || {};

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    // Find user and plan in parallel
    const [existUser, plans] = await Promise.all([
      User.findOne({ email }).select("subscription").lean(),
      Plan.find().lean(),
    ]);

    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const plan = plans.find(
      (p) =>
        existUser.subscription?.plan &&
        p._id.toString() === existUser.subscription.plan.toString()
    );

    if (!plan) {
      return res.status(400).json({
        success: false,
        message:
          "The subscription plan associated with your account could not be found. Please contact support for assistance.",
      });
    }

    // Feature mapping helper
    const formatFeature = (key, value) => {
      const featureMap = {
        videoUploadsLimit: (val) =>
          val === -1 ? "Unlimited video uploads" : `Up to ${val} video uploads`,
        videoStorageDays: (val) => `${val} days video storage`,
        storageLimit: (val) => `${val}MB storage limit`,
        bundleLimit: (val) =>
          val === -1 ? "Unlimited bundles" : `Up to ${val} bundles`,
        scheduleLimit: (val) =>
          val === -1 ? "Unlimited schedules" : `Up to ${val} schedules`,
        schedulingOptions: (val) => `Scheduling options: ${val.join(", ")}`,
        prioritySupport: (val) => (val ? "Priority support included" : ""),
        bulkUpload: (val) => (val ? "Bulk upload enabled" : ""),
      };
      return featureMap[key]?.(value) || "";
    };

    // Generate benefits HTML
    const benefitsHTML = Object.entries(plan.features || {})
      .map(([key, value]) => {
        const benefitText = formatFeature(key, value);
        return benefitText
          ? `
          <li style="margin:12px 0; padding-left:24px; position:relative;">
            <span style="color:#27ae60; position:absolute; left:0;">✓</span>
            <span style="color:#34495e;">${benefitText}</span>
          </li>`
          : "";
      })
      .filter(Boolean)
      .join("");

    await emailWorker("SubscriptionCompleted", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: `Welcome to ReelScheduler ${plan.name}`,
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Welcome to ${plan.name} Plan</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">Thank you for choosing ReelScheduler</p>
                </div>
                
                <div style="margin:30px 0; padding:20px; background-color:#f8f9fa; border-radius:8px;">
                  <h2 style="color:#2c3e50; font-size:20px; margin-bottom:15px;">Your Plan Benefits</h2>
                  <ul style="list-style:none; padding:0; margin:0;">${benefitsHTML}</ul>
                </div>

                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    Need assistance? Our support team is ready to help 24/7.<br>
                    Contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
                
                <hr style="border:none; border-top:1px solid #eee; margin:30px 0;">
                
                <div style="text-align:center;">
                  <p style="color:#95a5a6; font-size:12px; margin:0;">
                    ReelScheduler, Inc.<br>
                    Your trusted scheduling partner
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(`Subscription email sent to ${email}: ${info.messageId}`);
      } catch (error) {
        console.error("Failed to send subscription email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Subscription confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Subscription email processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during subscription processing",
      error: error.message,
    });
  }
};

// Subscription Cancelled
exports.SubscriptionCancelled = async (req, res) => {
  try {
    const { email } = req.body || {};

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    const existUser = await User.findOne({ email })
      .select("subscription")
      .lean();
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    await emailWorker("SubscriptionCancelled", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Your Subscription has been Cancelled",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Subscription Cancelled</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">We're sorry to see you go</p>
                </div>
                
                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any feedback or need assistance, please contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
                
                <hr style="border:none; border-top:1px solid #eee; margin:30px 0;">
                
                <div style="text-align:center;">
                  <p style="color:#95a5a6; font-size:12px; margin:0;">    
                    ReelScheduler, Inc.<br>
                    Your trusted scheduling partner
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(`Cancellation email sent to ${email}: ${info.messageId}`);
      } catch (error) {
        console.error("Failed to send cancellation email:", error);
      }
    }, 5, 3000);
    return res.status(202).json({
      success: true,
      message: "Cancellation confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Subscription cancellation processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during cancellation processing",
      error: error.message,
    });
  }
};

// Subscription Updated
exports.SubscriptionUpdated = async (req, res) => {
  try {
    const { email } = req.body || {};

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    const existUser = await User.findOne({ email })
      .select("subscription")
      .lean();
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    await emailWorker("SubscriptionUpdated", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Your Subscription has been Updated",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Subscription Updated</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">Your subscription details have been updated</p>
                </div>
                
                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any questions or need assistance, please contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
                
                <hr style="border:none; border-top:1px solid #eee; margin:30px 0;">
                
                <div style="text-align:center;">
                  <p style="color:#95a5a6; font-size:12px; margin:0;">
                    ReelScheduler, Inc.<br>
                    Your trusted scheduling partner
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Subscription updated email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send subscription updated email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Subscription update confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Subscription update processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during subscription update processing",
      error: error.message,
    });
  }
};

// Subscription Payment Failed
exports.SubscriptionPaymentFailed = async (req, res) => {
  try {
    const { email } = req.body || {};

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    const existUser = await User.findOne({ email })
      .select("subscription")
      .lean();
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    await emailWorker("SubscriptionPaymentFailed", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Subscription Payment Failed",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#e74c3c; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Payment Failed</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">There was an issue with your subscription payment</p>
                </div>
                
                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    Please update your payment information or contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a> for assistance.
                  </p>
                </div>
                
                <hr style="border:none; border-top:1px solid #eee; margin:30px 0;">
                
                <div style="text-align:center;">
                  <p style="color:#95a5a6; font-size:12px; margin:0;">
                    ReelScheduler, Inc.<br>
                    Your trusted scheduling partner
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Subscription payment failed email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error(
          "Failed to send subscription payment failed email:",
          error
        );
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Subscription payment failure confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Subscription payment failure processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during subscription payment failure processing",
      error: error.message,
    });
  }
};

// scheduleEmail function to send a scheduled email
exports.scheduleEmail = async (req, res) => {
  try {
    const { email, subject, html } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!subject || typeof subject !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email subject",
      });
    }

    if (!html || typeof html !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email content",
      });
    }

    await emailWorker("ScheduledEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject,
          html,
        });
        console.log(`Scheduled email sent to ${email}: ${info.messageId}`);
      } catch (error) {
        console.error("Failed to send scheduled email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Scheduled email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing scheduled email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during scheduled email processing",
      error: error.message,
    });
  }
};

// Subscription Expired
exports.SubscriptionExpired = async (req, res) => {
  try {
    const { email } = req.body || {};

    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    const existUser = await User.findOne({ email })
      .select("subscription")
      .lean();
    if (!existUser) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    await emailWorker("SubscriptionExpired", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Your Subscription has Expired",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#e74c3c; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Subscription Expired</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">Your subscription has expired</p>
                </div>
                
                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    Please renew your subscription to continue enjoying our services. If you have any questions, contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
                
                <hr style="border:none; border-top:1px solid #eee; margin:30px 0;">
                
                <div style="text-align:center;">
                  <p style="color:#95a5a6; font-size:12px; margin:0;">
                    ReelScheduler, Inc.<br>
                    Your trusted scheduling partner
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Subscription expired email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send subscription expired email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Subscription expired confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Subscription expired processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during subscription expired processing",
      error: error.message,
    });
  }
};

// Upcoming Schedule Email
exports.UpcomingScheduleEmail = async (req, res) => {
  try {
    const { email, scheduleDetails } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!scheduleDetails || typeof scheduleDetails !== "object") {
      return res.status(400).json({
        success: false,
        message: "Invalid schedule details",
      });
    }

    await emailWorker("UpcomingScheduleEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Upcoming Schedule Reminder",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Upcoming Schedule Reminder</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">You have an upcoming schedule</p>
                </div>
                
                <div style="margin-top:30px;">
                  <h2 style="color:#2c3e50; font-size:20px;">Schedule Details</h2>
                  <p style="color:#34495e;">${scheduleDetails.description}</p>
                  <p style="color:#34495e;">Date and Time: ${new Date(
                    scheduleDetails.date
                  ).toLocaleString()}</p>
                </div>

                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any questions or need assistance, please contact us at <a href="mailto:${
                      process.env.EMAIL_HOST_USER
                    }" style="color:#3498db; text-decoration:none;">${
            process.env.EMAIL_HOST_USER
          }</a>
                  </p> 
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Upcoming schedule email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send upcoming schedule email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Upcoming schedule confirmation is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Upcoming schedule processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during upcoming schedule processing",
      error: error.message,
    });
  }
};

// Video Upload Notification Email
exports.VideoUploadNotificationEmail = async (req, res) => {
  try {
    const { email, videoDetails } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!videoDetails || typeof videoDetails !== "object") {
      return res.status(400).json({
        success: false,
        message: "Invalid video details",
      });
    }

    await emailWorker("VideoUploadNotificationEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Video Upload Notification",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">Video Upload Notification</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">Your video has been successfully uploaded</p>
                </div>
                
                <div style="margin-top:30px;">
                  <h2 style="color:#2c3e50; font-size:20px;">Video Details</h2>
                  <p style="color:#34495e;">Title: ${videoDetails.title}</p>
                  <p style="color:#34495e;">Description: ${
                    videoDetails.description
                  }</p>
                  <p style="color:#34495e;">Uploaded on: ${new Date(
                    videoDetails.uploadDate
                  ).toLocaleString()}</p>
                </div>

                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any questions or need assistance, please contact us at <a href="mailto:${
                      process.env.EMAIL_HOST_USER
                    }" style="color:#3498db; text-decoration:none;">${
            process.env.EMAIL_HOST_USER
          }</a>
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Video upload notification email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send video upload notification email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Video upload notification is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Video upload notification processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during video upload notification processing",
      error: error.message,
    });
  }
};

// Feedback Request Email
exports.FeedbackRequestEmail = async (req, res) => {
  try {
    const { email, feedbackLink } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!feedbackLink || typeof feedbackLink !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid feedback link",
      });
    }

    await emailWorker("FeedbackRequestEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "We Value Your Feedback",
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">We Value Your Feedback</h1>
                  <p style="color:#7f8c8d; font-size:16px; margin-top:10px;">Help us improve by sharing your thoughts</p>
                </div>
                
                <div style="margin-top:30px;">
                  <p style="color:#34495e;">We would love to hear your feedback on our service. Please take a moment to fill out our feedback form:</p>
                  <p><a href="${feedbackLink}" style="color:#3498db; text-decoration:none;">Give Feedback</a></p>
                </div>

                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any questions or need assistance, please contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Feedback request email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send feedback request email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Feedback request email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Feedback request email processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during feedback request email processing",
      error: error.message,
    });
  }
};

// user grab attention email
exports.UserGrabAttentionEmail = async (req, res) => {
  try {
    const { email, subject, message } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!subject || typeof subject !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email subject",
      });
    }

    if (!message || typeof message !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email content",
      });
    }

    await emailWorker("UserGrabAttentionEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject,
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="color:#2c3e50; font-family:'Helvetica Neue',Helvetica,Arial,sans-serif; font-size:28px; margin:0;">${subject}</h1>
                </div>
                
                <div style="margin-top:30px;">
                  <p style="color:#34495e;">${message}</p>
                </div>

                <div style="margin-top:30px; text-align:center;">
                  <p style="color:#7f8c8d; font-size:14px; line-height:1.6;">
                    If you have any questions or need assistance, please contact us at <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">${process.env.EMAIL_HOST_USER}</a>
                  </p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `User grab attention email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send user grab attention email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "User grab attention email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("User grab attention email processing error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during user grab attention email processing",
      error: error.message,
    });
  }
};

// inviteUserEmail function to send an invitation email
exports.inviteUserEmail = async (req, res) => {
  try {
    const { email, subject, html } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!subject || typeof subject !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email subject",
      });
    }

    if (!html || typeof html !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email content",
      });
    }

    await emailWorker("InviteUserEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject,
          html,
        });
        console.log(`Invitation email sent to ${email}: ${info.messageId}`);
      } catch (error) {
        console.error("Failed to send invitation email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Invitation email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing invitation email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during invitation email processing",
      error: error.message,
    });
  }
};

// FeedBack Email
exports.FeedbackEmail = async (req, res) => {
  try {
    const { email, subject, message } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!subject || typeof subject !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email subject",
      });
    }
    if (!message || typeof message !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email content",
      });
    }

    await emailWorker("FeedbackResponseEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: "<EMAIL>",
          subject,
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="font-size:24px; color:#333333;">Feedback Response</h1>
                  <p style="font-size:16px; color:#666666;">${subject}</p>
                  <p style="font-size:16px; color:#666666;">${message}</p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(
          `Feedback response email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send feedback response email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Feedback response email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing feedback response email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during feedback response email processing",
      error: error.message,
    });
  }
};

// Contact Us Email
exports.ContactUsEmail = async (req, res) => {
  try {
    const { email, subject, message } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!subject || typeof subject !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email subject",
      });
    }

    if (!message || typeof message !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid email content",
      });
    }

    await emailWorker("ContactUsEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: "<EMAIL>",
          subject,
          html: `
            <div style="background-color:#f8f9fa; padding:40px 0;">
              <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:12px; box-shadow:0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align:center; margin-bottom:30px;">
                  <h1 style="font-size:24px; color:#333333;">Contact Us</h1>
                  <p style="font-size:16px; color:#666666;">${subject}</p>
                  <p style="font-size:16px; color:#666666;">${message}</p>
                </div>
              </div>
            </div>
          `,
        });
        console.log(`Contact us email sent to ${email}: ${info.messageId}`);
      } catch (error) {
        console.error("Failed to send contact us email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Contact us email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing contact us email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during contact us email processing",
      error: error.message,
    });
  }
};

// Account Suspended Email
exports.AccountSuspend = async (req, res) => {
  try {
    const { email, reason, status } = req.body;

    // Input validation
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    await emailWorker("AccountSuspend", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: "Your Account Has Been Suspended",
          html: `
        <div style="background-color:#f4f6f8; padding:40px 0; font-family:Arial, sans-serif;">
          <div style="background-color:#ffffff; max-width:600px; margin:0 auto; padding:40px; border-radius:10px; box-shadow:0 2px 8px rgba(0,0,0,0.1);">
            <div style="text-align:center; margin-bottom:30px;">
              <h1 style="font-size:26px; color:${
                status === "suspend" ? "#e74c3c" : "#27ae60"
              }; margin-bottom:10px;">
                Account ${status === "suspend" ? "Suspended" : "Activated"}
              </h1>
              <p style="font-size:16px; color:#555;">
                ${
                  status === "suspend"
                    ? "We noticed some unusual activity or a policy violation on your account."
                    : "We’re excited to let you know that your ReelScheduler account is now active."
                }
              </p>
            </div>

            ${
              status === "suspend"
                ? `<div style="margin-top:20px; padding:20px; background-color:#fcebea; border-left:4px solid #e74c3c; border-radius:6px;">
                    <p style="font-size:15px; color:#c0392b; margin:0;">
                      ${
                        reason
                          ? `<strong>Reason:</strong> ${reason}`
                          : "Your account has been suspended due to a policy violation or other concern. For more details, please reach out to our support team."
                      }
                    </p>
                  </div>`
                : `<div style="margin-top:20px; padding:20px; background-color:#eafaf1; border-left:4px solid #27ae60; border-radius:6px;">
                    <p style="font-size:15px; color:#2c3e50; margin:0;">
                      Welcome back! You can now access all features of your account and continue scheduling your content effortlessly.
                    </p>
                  </div>`
            }

            <div style="margin-top:30px; text-align:center;">
              <p style="font-size:14px; color:#7f8c8d;">
                ${
                  status === "suspend"
                    ? "If you believe this was done in error or need help resolving the issue, please don’t hesitate to contact us."
                    : "If you have any questions or need assistance, we’re here to help."
                }
              </p>
              <p style="margin-top:10px;">
                <a href="mailto:${
                  process.env.EMAIL_HOST_USER
                }" style="color:#3498db; font-weight:bold; text-decoration:none;">
                  ${process.env.EMAIL_HOST_USER}
                </a>
              </p>
            </div>

            <div style="margin-top:40px; text-align:center; font-size:12px; color:#b2bec3;">
              <p>Thank you for using ReelScheduler.</p>
            </div>
          </div>
        </div>
      `,
        });
        console.log(
          `Account suspension email sent to ${email}: ${info.messageId}`
        );
      } catch (error) {
        console.error("Failed to send account suspension email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Account suspension email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing account suspend email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during account suspend email processing",
      error: error.message,
    });
  }
};

// SendContentLinkEmail function to send content link via email
exports.SendContentLinkEmail = async (req, res) => {
  try {
    const { email, contentTitle, downloadUrl, recipientName } = req.body;

    // Input validation
    if (
      !email ||
      typeof email !== "string" ||
      !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid email address",
      });
    }

    if (!contentTitle || typeof contentTitle !== "string") {
      return res.status(400).json({
        success: false,
        message: "Invalid content title",
      });
    }

    await emailWorker("SendContentLinkEmail", async () => {
      try {
        const info = await transporter.sendMail({
          from: process.env.EMAIL_HOST_USER,
          to: [email],
          subject: `Content Shared: ${contentTitle}`,
          html: `
            <div style="background-color:#ffffff; border-radius:8px; padding:20px; max-width:600px; margin:20px auto; box-shadow:0 2px 8px rgba(0, 0, 0, 0.1); font-family: Arial, sans-serif;">
              <h2 style="color:#444; margin-bottom:20px;">Content Shared with You</h2>
              
              <div style="background-color:#f8f9fa; border-radius:6px; padding:15px; margin-bottom:20px;">
                <h3 style="color:#333; margin:0 0 10px 0; font-size:18px;">${contentTitle}</h3>
                <p style="color:#666; margin:0; font-size:14px;">Someone has shared this content with you from ReelScheduler.</p>
              </div>

              <div style="text-align:center; margin:30px 0;">
                ${downloadUrl ? `
                <br>
                <a href="${downloadUrl}" 
                   style="display:inline-block; background:#28a745; color:#fff; padding:12px 24px; border-radius:6px; text-decoration:none; font-weight:bold; font-size:16px;">
                  Download Content
                </a>
                ` : ''}
              </div>

              <div style="margin-top:30px; padding-top:20px; border-top:1px solid #eee;">
                <p style="color:#666; font-size:14px; margin-bottom:10px;">
                  If the buttons don't work, you can copy and paste these links into your browser:
                </p>
                ${downloadUrl ? `
                <p style="word-break:break-all; color:#28a745; font-size:12px; background-color:#f8f9fa; padding:10px; border-radius:4px;">
                  <strong>Download Content:</strong> ${downloadUrl}
                </p>
                ` : ''}
              </div>

              <div style="margin-top:30px; text-align:center; font-size:12px; color:#b2bec3;">
                <p>This content was shared via ReelScheduler.</p>
                <p>If you have any questions, please contact us at 
                  <a href="mailto:${process.env.EMAIL_HOST_USER}" style="color:#3498db; text-decoration:none;">
                    ${process.env.EMAIL_HOST_USER}
                  </a>
                </p>
              </div>
            </div>
          `,
        });
        console.log(
          "Content link email sent to:",
          email,
          "Message ID:",
          info.messageId
        );
      } catch (error) {
        console.error("Failed to send content link email:", error);
      }
    }, 5, 3000);

    return res.status(202).json({
      success: true,
      message: "Content link email is being processed",
      note: "You'll receive the email shortly. Check spam folder if not in inbox.",
    });
  } catch (error) {
    console.error("Error processing content link email:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during content link email processing",
      error: error.message,
    });
  }
};
