const Plan = require("../models/plan");
const User = require("../models/user");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const sendEmailController = require("./sendEmailController");

// Get all plans
exports.getPlans = async (req, res) => {
  try {
    const plans = await Plan.find({ isActive: true }).sort({ sortOrder: 1 });

    res.status(200).json({
      success: true,
      plans,
    });
  } catch (error) {
    console.error("Error fetching plans:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plans",
      error: error.message,
    });
  }
};

// Get a specific plan
exports.getPlan = async (req, res) => {
  try {
    const planId = req.params.id;

    // Check if planId is valid
    if (!planId || planId === "undefined" || planId === "null") {
      return res.status(400).json({
        success: false,
        message: "Invalid plan ID provided",
      });
    }

    const plan = await Plan.findById(planId).populate("features");

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    res.status(200).json({
      success: true,
      plan,
    });
  } catch (error) {
    console.error("Error fetching plan:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plan",
      error: error.message,
    });
  }
};

// Get subscription details
exports.getSubscriptionDetails = async (req, res) => {
  try {
    // If subscriptionData is already attached by middleware, use it
    if (req.subscriptionData) {
      return res.status(200).json({
        success: true,
        subscription: {
          plan: req.subscriptionData.plan,
          status: req.subscriptionData.status,
          isFree: req.subscriptionData.isFree,
          currentPeriodEnd: req.subscriptionData.currentPeriodEnd,
          features: req.subscriptionData.features
        }
      });
    }

    // Otherwise, fetch it directly (fallback)
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId).populate({
      path: "subscription.plan",
      populate: {
        path: "features",
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // If user has no subscription, return free plan
    if (!user.subscription || !user.subscription.plan) {
      const freePlan = await Plan.findOne({ name: "Free" }).populate("features");

      return res.status(200).json({
        success: true,
        subscription: {
          plan: freePlan || { name: "Free", features: [] },
          status: "active",
          isFree: true,
          features: freePlan ? freePlan.features : []
        },
      });
    }

    // Determine if it's a free plan
    const isFree = user.subscription.plan.name === "Free";

    res.status(200).json({
      success: true,
      subscription: {
        plan: user.subscription.plan,
        status: user.subscription.status,
        isFree,
        currentPeriodEnd: user.subscription.currentPeriodEnd,
        features: user.subscription.plan.features
      },
    });
  } catch (error) {
    console.error("Error fetching subscription details:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch subscription details",
      error: error.message,
    });
  }
};

/**
 * Get all available features
 */
exports.getAllFeatures = async (req, res) => {
  try {
    const features = await Feature.find({});
    
    res.status(200).json({
      success: true,
      features
    });
  } catch (error) {
    console.error("Error fetching features:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch features",
      error: error.message,
    });
  }
};

// Create subscription
exports.createSubscription = async (req, res) => {
  try {
    const { planId, paymentMethodId, billingCycle } = req.body;
    const userId = req.ownerId || req.user.id;

    // Find the plan
    const plan = await Plan.findById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // For free plan, just update the user's subscription
    if (plan.price.monthly === 0 && plan.price.yearly === 0) {
      await User.findByIdAndUpdate(userId, {
        subscription: {
          plan: plan._id,
          status: "active",
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          cancelAtPeriodEnd: false,
        },
      });

      return res.status(200).json({
        success: true,
        message: "Free plan subscription activated",
      });
    }

    // For paid plans, handle Stripe integration
    // This is a simplified version - in a real app, you'd need to handle more cases

    // Get or create Stripe customer
    const user = await User.findById(userId);
    let customerId = user.subscription?.stripeCustomerId;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: { userId: user._id.toString() },
      });
      customerId = customer.id;
      user.subscription.stripeCustomerId = customerId;
      await user.save();
    }

    // Create subscription
    const priceId =
      billingCycle === "yearly" ? plan.yearlyPriceId : plan.monthlyPriceId;

    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: "default_incomplete",
      expand: ["latest_invoice.payment_intent"],
    }, {
      idempotencyKey: `subscr-${userId}-${plan._id}-${billingCycle}`
    });

    // Update user's subscription
    await User.findByIdAndUpdate(userId, {
      subscription: {
        plan: plan._id,
        status: subscription.status,
        stripeCustomerId: customerId,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: subscription.latest_invoice.payment_intent.client_secret,
      subscriptionId: subscription.id,
    });
  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create subscription",
      error: error.message,
    });
  }
};

// Helper to check if UPI is supported for this Stripe account
async function isUpiSupported() {
  try {
    const account = await stripe.accounts.retrieve();
    // UPI is only available for India accounts in INR
    return account.country === "IN";
  } catch (e) {
    console.error("Failed to check Stripe account country:", e);
    return false;
  }
}

// Create UPI Payment Intent
exports.createUpiPayment = async (req, res) => {
  try {
    // Check if UPI is supported
    const upiSupported = await isUpiSupported();
    if (!upiSupported) {
      return res.status(400).json({
        success: false,
        message:
          "UPI payments are not supported on this Stripe account. Please use a different payment method.",
      });
    }

    const { planId, billingCycle } = req.body;
    const userId = req.ownerId || req.user.id;

    // Find the plan
    const plan = await Plan.findById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // Get or create Stripe customer
    const user = await User.findById(userId);
    let stripeCustomerId = user.subscription?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString(),
        },
      });
      stripeCustomerId = customer.id;
    }

    // Calculate amount based on billing cycle
    const amount =
      billingCycle === "monthly"
        ? plan.price.monthly * 100
        : plan.price.yearly * 100;

    // Create a PaymentIntent for UPI (only UPI, not card)
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount),
      currency: "inr", // Use INR for UPI payments
      customer: stripeCustomerId,
      payment_method_types: ["upi"], // Only UPI
      metadata: {
        planId: plan._id.toString(),
        billingCycle,
        userId: user._id.toString(),
      },
      description: `${plan.name} Plan (${billingCycle})`,
    }, {
      idempotencyKey: `upi-${user._id}-${plan._id}-${billingCycle}`
    });

    // Update user with Stripe customer ID
    if (!user.subscription) {
      user.subscription = {};
    }
    user.subscription.stripeCustomerId = stripeCustomerId;
    await user.save();

    // Return the client secret and payment intent ID
    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      redirectUrl: `/subscription/upi-verify?payment_intent=${paymentIntent.id}`,
    });
  } catch (error) {
    console.error("Error creating UPI payment:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create UPI payment",
      error: error.message,
    });
  }
};

// Check UPI payment status
exports.checkUpiPaymentStatus = async (req, res) => {
  try {
    const { paymentIntentId } = req.params;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: "Payment intent ID is required",
      });
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status === "succeeded") {
      // Payment was successful, create subscription
      const { planId, billingCycle, userId } = paymentIntent.metadata;

      // Find the plan
      const plan = await Plan.findById(planId);
      if (!plan) {
        return res.status(404).json({
          success: false,
          status: "failed",
          message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
        });
      }

      // Find the user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          status: "failed",
          message: "User not found",
        });
      }

      // Create subscription in Stripe
      const subscription = await stripe.subscriptions.create({
        customer: paymentIntent.customer,
        items: [
          {
            price:
              billingCycle === "monthly"
                ? plan.stripePriceIds.monthly
                : plan.stripePriceIds.yearly,
          },
        ],
        payment_behavior: "default_incomplete",
        payment_settings: {
          payment_method_types: ["upi"],
          save_default_payment_method: "on_subscription",
        },
        expand: ["latest_invoice.payment_intent"],
      }, {
        idempotencyKey: `subscr-upi-${user._id}-${plan._id}-${billingCycle}`
      });

      // Update user subscription in database
      if (!user.subscription) {
        user.subscription = {};
      }

      user.subscription = {
        ...user.subscription,
        plan: plan._id,
        status: "active",
        stripeCustomerId: paymentIntent.customer,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: false,
      };

      await user.save();

      return res.status(200).json({
        success: true,
        status: "succeeded",
        message: "Payment successful and subscription created",
      });
    } else if (
      paymentIntent.status === "canceled" ||
      paymentIntent.status === "requires_payment_method"
    ) {
      return res.status(200).json({
        success: false,
        status: "failed",
        message: "Payment was not completed or was canceled",
      });
    } else {
      // Payment is still pending
      return res.status(200).json({
        success: true,
        status: "pending",
        message: "Payment is still being processed",
      });
    }
  } catch (error) {
    console.error("Error checking UPI payment status:", error);
    res.status(500).json({
      success: false,
      status: "failed",
      message: "Failed to check payment status",
      error: error.message,
    });
  }
};

// Save subscription data (for free plans or additional info)
exports.saveSubscriptionData = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;
    const { planId, customFields } = req.body;

    if (!planId) {
      return res.status(400).json({
        success: false,
        message: "Plan ID is required",
      });
    }

    // Find the plan
    const plan = await Plan.findById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // Find the user and update their subscription data
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Initialize subscription object if it doesn't exist
    if (!user.subscription) {
      user.subscription = {};
    }

    // Determine appropriate period end date based on plan type
    const periodEnd =
      plan.name === "Free"
        ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year for free plans
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days for other plans without payment

    // Update user's subscription data
    user.subscription = {
      ...user.subscription,
      plan: plan._id,
      status: "active",
      customFields: customFields || {},
      currentPeriodStart: new Date(),
      currentPeriodEnd: periodEnd,
      cancelAtPeriodEnd: false,
      updatedAt: new Date(),
    };

    await user.save();

    res.status(200).json({
      success: true,
      message: "Subscription data saved successfully",
      subscription: {
        plan: plan._id,
        planName: plan.name,
        status: user.subscription.status,
        currentPeriodEnd: user.subscription.currentPeriodEnd,
        customFields: user.subscription.customFields,
      },
    });
  } catch (error) {
    console.error("Error saving subscription data:", error);
    res.status(500).json({
      success: false,
      message: "Failed to save subscription data",
      error: error.message,
    });
  }
};

// Cancel subscription
exports.cancelSubscription = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;

    // Get the user
    const user = await User.findById(userId);
    if (
      !user ||
      !user.subscription ||
      !user.subscription.stripeSubscriptionId
    ) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found",
      });
    }

    // Cancel the subscription in Stripe
    await stripe.subscriptions.update(user.subscription.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    // Update user's subscription in database
    user.subscription.cancelAtPeriodEnd = true;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Subscription will be canceled at the end of the billing period",
    });
  } catch (error) {
    console.error("Error canceling subscription:", error);
    res.status(500).json({
      success: false,
      message: "Failed to cancel subscription",
      error: error.message,
    });
  }
};

// Reactivate a canceled subscription
exports.reactivateSubscription = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;

    // Get the user
    const user = await User.findById(userId);
    if (
      !user ||
      !user.subscription ||
      !user.subscription.stripeSubscriptionId
    ) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found",
      });
    }

    // Reactivate the subscription in Stripe
    await stripe.subscriptions.update(user.subscription.stripeSubscriptionId, {
      cancel_at_period_end: false,
    });

    // Update user's subscription in database
    user.subscription.cancelAtPeriodEnd = false;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Subscription reactivated successfully",
    });
  } catch (error) {
    console.error("Error reactivating subscription:", error);
    res.status(500).json({
      success: false,
      message: "Failed to reactivate subscription",
      error: error.message,
    });
  }
};

// Get subscription details
exports.getSubscriptionDetails = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;

    // Find the user with populated plan and features
    const user = await User.findById(userId).populate({
      path: "subscription.plan",
      populate: {
        path: "features",
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // If user has no subscription, return free plan
    if (!user.subscription || !user.subscription.plan) {
      // Find free plan
      const freePlan = await Plan.findOne({ name: "Free" });

      return res.status(200).json({
        success: true,
        subscription: {
          plan: freePlan || { name: "Free", features: [] },
          status: "active",
          isFree: true,
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          cancelAtPeriodEnd: false,
          customFields: {},
        },
      });
    }

    // Get additional details from Stripe if available
    let stripeSubscription = null;
    let paymentMethod = null;

    if (user.subscription.stripeSubscriptionId) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(
          user.subscription.stripeSubscriptionId,
          { expand: ["default_payment_method"] }
        );

        if (stripeSubscription.default_payment_method) {
          paymentMethod = stripeSubscription.default_payment_method;
        } else if (stripeSubscription.customer) {
          // Get customer's default payment method
          const paymentMethods = await stripe.paymentMethods.list({
            customer: stripeSubscription.customer,
            type: "card",
            limit: 1,
          });

          if (paymentMethods.data.length > 0) {
            paymentMethod = paymentMethods.data[0];
          }
        }
      } catch (stripeError) {
        console.error("Error fetching Stripe subscription:", stripeError);
        // Continue without Stripe data if there's an error
      }
    }

    // Determine if it's a free plan
    const isFree = user.subscription.plan.name === "Free";

    res.status(200).json({
      success: true,
      subscription: {
        plan: user.subscription.plan,
        status: user.subscription.status,
        isFree,
        currentPeriodStart: user.subscription.currentPeriodStart,
        currentPeriodEnd: user.subscription.currentPeriodEnd,
        cancelAtPeriodEnd: user.subscription.cancelAtPeriodEnd,
        customFields: user.subscription.customFields,
      },
    });
  } catch (error) {
    console.error("Error fetching subscription details:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch subscription details",
      error: error.message,
    });
  }
};

// Handle Stripe webhook events
exports.handleWebhook = async (req, res) => {
  const signature = req.headers["stripe-signature"];

  if (!signature) {
    return res.status(400).json({
      success: false,
      message: "Stripe signature missing",
    });
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body ? req.body : req.rawBody, // Support both body-parser and raw
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );
  } catch (err) {
    console.error("Webhook signature verification failed:", err.message);
    return res.status(400).json({
      success: false,
      message: "Webhook signature verification failed",
    });
  }

  // Process the event
  try {
    switch (event.type) {
      case "customer.subscription.updated":
        await handleSubscriptionUpdated(event.data.object);
        break;
      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(event.data.object);
        break;
      case "invoice.payment_succeeded":
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      case "invoice.payment_failed":
        await handleInvoicePaymentFailed(event.data.object);
        break;
      // Add more event types as needed
      default:
        console.log(`Unhandled Stripe event type: ${event.type}`);
    }
    res.status(200).json({ received: true });
  } catch (err) {
    console.error("Error processing Stripe webhook event:", err);
    res.status(500).json({ success: false, message: "Webhook processing error" });
  }
};

// Helper functions for webhook events
async function handleSubscriptionUpdated(subscription) {
  try {
    // Find user by customer ID
    const user = await User.findOne({
      "subscription.stripeCustomerId": subscription.customer,
    });
    if (!user) {
      console.error("User not found for customer:", subscription.customer);
      return;
    }

    // Update subscription details
    user.subscription.status = subscription.status;
    user.subscription.currentPeriodStart = new Date(
      subscription.current_period_start * 1000
    );
    user.subscription.currentPeriodEnd = new Date(
      subscription.current_period_end * 1000
    );
    user.subscription.cancelAtPeriodEnd = subscription.cancel_at_period_end;

    await user.save();
    console.log(`Subscription updated for user ${user._id}`);

    // Send subscription updated email
    if (user.email) {
      sendEmailController.SubscriptionUpdated({ body: { email: user.email } }, { status: () => ({ json: () => {} }) });
    }
  } catch (error) {
    console.error("Error handling subscription updated:", error);
  }
}


async function handleSubscriptionDeleted(subscription) {
  try {
    // Find user by customer ID
    const user = await User.findOne({
      "subscription.stripeCustomerId": subscription.customer,
    });
    if (!user) {
      console.error("User not found for customer:", subscription.customer);
      return;
    }

    // Get free plan
    const freePlan = await Plan.findOne({ name: "Free" });

    // Update subscription details
    user.subscription.status = "canceled";
    user.subscription.plan = freePlan ? freePlan._id : null;

    await user.save();
    console.log(`Subscription canceled for user ${user._id}`);

    // Send subscription cancelled email
    if (user.email) {
      sendEmailController.SubscriptionCancelled({ body: { email: user.email } }, { status: () => ({ json: () => {} }) });
    }
  } catch (error) {
    console.error("Error handling subscription deleted:", error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  try {
    if (!invoice.subscription) return;

    // Find user by customer ID
    const user = await User.findOne({
      "subscription.stripeCustomerId": invoice.customer,
    });
    if (!user) {
      console.error("User not found for customer:", invoice.customer);
      return;
    }

    // Update subscription status to active if it's not already
    if (user.subscription.status !== "active") {
      user.subscription.status = "active";
      await user.save();
      console.log(`Subscription activated for user ${user._id}`);
    }
    // Send subscription renewal email (reuse SubscriptionUpdated)
    if (user.email) {
      sendEmailController.SubscriptionUpdated({ body: { email: user.email } }, { status: () => ({ json: () => {} }) });
    }
  } catch (error) {
    console.error("Error handling invoice payment succeeded:", error);
  }
}

async function handleInvoicePaymentFailed(invoice) {
  try {
    if (!invoice.subscription) return;

    // Find user by customer ID
    const user = await User.findOne({
      "subscription.stripeCustomerId": invoice.customer,
    });
    if (!user) {
      console.error("User not found for customer:", invoice.customer);
      return;
    }

    // Update subscription status
    user.subscription.status = "past_due";
    await user.save();
    console.log(`Subscription marked as past_due for user ${user._id}`);

    // Send payment failed email
    if (user.email) {
      sendEmailController.SubscriptionPaymentFailed({ body: { email: user.email } }, { status: () => ({ json: () => {} }) });
    }
  } catch (error) {
    console.error("Error handling invoice payment failed:", error);
  }
}

// Get billing history
exports.getBillingHistory = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);

    if (!user.subscription?.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        message: "No customer ID found for this user",
      });
    }

    const invoices = await stripe.invoices.list({
      customer: user.subscription.stripeCustomerId,
      limit: 10, // Limit to 10 invoices
    });

    res.status(200).json({
      success: true,
      invoices: invoices.data.map((invoice) => ({
        id: invoice.id,
        amount_due: invoice.amount_due,
        currency: invoice.currency,
        status: invoice.status,
        created: new Date(invoice.created * 1000),
        paid: invoice.paid,
      })),
    });
  } catch (error) {
    console.error("Error fetching billing history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch billing history",
      error: error.message,
    });
  }
};

// Create a subscription with UPI payment
exports.createUpiSubscription = async (req, res) => {
  try {
    // Check if UPI is supported
    const upiSupported = await isUpiSupported();
    if (!upiSupported) {
      return res.status(400).json({
        success: false,
        message:
          "UPI payments are not supported on this Stripe account. Please use a different payment method.",
      });
    }

    const { upiId, planId, billingCycle } = req.body;
    const userId = req.ownerId || req.user.id;

    if (!upiId || !planId || !billingCycle) {
      return res.status(400).json({
        success: false,
        message: "UPI ID, plan ID, and billing cycle are required",
      });
    }

    // Find the plan
    const plan = await Plan.findById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // Find or create Stripe customer
    const user = await User.findById(userId);
    let stripeCustomerId = user.subscription?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString(),
        },
      });
      stripeCustomerId = customer.id;
    }

    // Get the price ID based on billing cycle
    const priceId =
      billingCycle === "monthly"
        ? plan.stripePriceIds.monthly
        : plan.stripePriceIds.yearly;

    // Create a PaymentIntent for UPI
    const paymentIntent = await stripe.paymentIntents.create({
      amount:
        billingCycle === "monthly"
          ? plan.price.monthly * 100
          : plan.price.yearly * 100,
      currency: "inr", // Use INR for UPI payments
      customer: stripeCustomerId,
      payment_method_types: ["upi"],
      metadata: {
        planId: plan._id.toString(),
        billingCycle,
        userId: user._id.toString(),
      },
    }, {
      idempotencyKey: `upi-${user._id}-${plan._id}-${billingCycle}`
    });

    // Update user with Stripe customer ID
    if (!user.subscription) {
      user.subscription = {};
    }
    user.subscription.stripeCustomerId = stripeCustomerId;
    await user.save();

    // Return the client secret and payment intent ID
    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      redirectUrl: `/subscription/upi-verify?payment_intent=${paymentIntent.id}`,
    });
  } catch (error) {
    console.error("Error creating UPI subscription:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create UPI subscription",
      error: error.message,
    });
  }
};

// Check UPI payment status
exports.checkUpiPaymentStatus = async (req, res) => {
  try {
    const { paymentIntentId } = req.params;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: "Payment intent ID is required",
      });
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status === "succeeded") {
      // Payment was successful, create subscription
      const { planId, billingCycle, userId } = paymentIntent.metadata;

      // Find the plan
      const plan = await Plan.findById(planId);
      if (!plan) {
        return res.status(404).json({
          success: false,
          status: "failed",
          message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
        });
      }

      // Find the user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          status: "failed",
          message: "User not found",
        });
      }

      // Create subscription in Stripe
      const subscription = await stripe.subscriptions.create({
        customer: paymentIntent.customer,
        items: [
          {
            price:
              billingCycle === "monthly"
                ? plan.stripePriceIds.monthly
                : plan.stripePriceIds.yearly,
          },
        ],
        payment_behavior: "default_incomplete",
        payment_settings: {
          payment_method_types: ["upi"],
          save_default_payment_method: "on_subscription",
        },
        expand: ["latest_invoice.payment_intent"],
      }, {
        idempotencyKey: `subscr-upi-${user._id}-${plan._id}-${billingCycle}`
      });

      // Update user subscription in database
      if (!user.subscription) {
        user.subscription = {};
      }

      user.subscription = {
        ...user.subscription,
        plan: plan._id,
        status: "active",
        stripeCustomerId: paymentIntent.customer,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: false,
      };

      await user.save();

      return res.status(200).json({
        success: true,
        status: "succeeded",
        message: "Payment successful and subscription created",
      });
    } else if (
      paymentIntent.status === "canceled" ||
      paymentIntent.status === "requires_payment_method"
    ) {
      return res.status(200).json({
        success: false,
        status: "failed",
        message: "Payment was not completed or was canceled",
      });
    } else {
      // Payment is still pending
      return res.status(200).json({
        success: true,
        status: "pending",
        message: "Payment is still being processed",
      });
    }
  } catch (error) {
    console.error("Error checking UPI payment status:", error);
    res.status(500).json({
      success: false,
      status: "failed",
      message: "Failed to check payment status",
      error: error.message,
    });
  }
};

// Create Stripe Checkout session
exports.createCheckoutSession = async (req, res) => {
  try {
    const { planId, billingCycle, successUrl, cancelUrl, paymentMethod } =
      req.body;
    const userId = req.ownerId || req.user.id;

    // Find the plan
    const Plan = require("../models/plan");
    const User = require("../models/user");
    const plan = await Plan.findById(planId);

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "The requested subscription plan could not be found. Please check your plan selection or contact support.",
      });
    }

    // Get or create Stripe customer
    const user = await User.findById(userId);
    let customerId = user.subscription?.stripeCustomerId;

    if (!customerId) {
      const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || user.email,
        metadata: {
          userId: userId.toString(),
        },
      });
      customerId = customer.id;

      // Update user with Stripe customer ID
      if (!user.subscription) {
        user.subscription = {};
      }
      user.subscription.stripeCustomerId = customerId;
      await user.save();
    }

    // Determine price based on billing cycle
    const amount =
      billingCycle === "yearly" ? plan.yearlyPrice : plan.monthlyPrice;
    const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

    // Set payment method types based on selected method
    const paymentMethodTypes = ["card"]; // Default
    if (paymentMethod === "upi") {
      paymentMethodTypes.push("upi");
    }

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: paymentMethodTypes,
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `${plan.name} Plan (${billingCycle})`,
              description:
                plan.description || `Subscription to ${plan.name} plan`,
              images: plan.imageUrl ? [plan.imageUrl] : undefined,
            },
            unit_amount: Math.round(amount * 100), // Convert to cents
            recurring: {
              interval: billingCycle === "monthly" ? "month" : "year",
              interval_count: 1,
            },
          },
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        planId: planId,
        billingCycle: billingCycle,
        userId: userId.toString(),
      },
      allow_promotion_codes: true,
    });

    // Return the session URL
    res.status(200).json({
      success: true,
      url: session.url,
      sessionId: session.id,
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create checkout session",
      error: error.message,
    });
  }
};

// Verify Stripe Checkout session (Stripe Checkout)
exports.verifyCheckoutSession = async (req, res) => {
  try {
    const { sessionId } = req.body;
    const userId = req.ownerId || req.user.id;

    // Input validation
    if (!sessionId || typeof sessionId !== "string") {
      return res.status(400).json({
        success: false,
        message: "Valid session ID is required",
      });
    }

    // Retrieve and validate session
    const session = await stripe.checkout.sessions.retrieve(sessionId.trim(), {
      expand: ["subscription", "customer"],
    });

    if (!session || session.payment_status !== "paid") {
      return res.status(400).json({
        success: false,
        message: "Invalid or unpaid session",
        payment_status: session?.payment_status,
      });
    }

    // Verify session belongs to user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (
      session.metadata?.userId &&
      session.metadata.userId !== userId.toString()
    ) {
      return res.status(403).json({
        success: false,
        message: "Unauthorized session access",
      });
    }

    // Get subscription from session
    let subscription = session.subscription;
    if (!subscription) {
      return res.status(400).json({
        success: false,
        message: "No subscription found in session",
      });
    }
    if (typeof subscription === "string") {
      subscription = await stripe.subscriptions.retrieve(subscription);
    }

    // Get plan details
    const plan = await Plan.findById(session.metadata?.planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "Subscription plan not found",
      });
    }

    let currentPeriodStart, currentPeriodEnd;
    if (
      subscription.items &&
      subscription.items.data &&
      subscription.items.data.length > 0
    ) {
      currentPeriodStart = subscription.items.data[0].current_period_start;
      currentPeriodEnd = subscription.items.data[0].current_period_end;
    }

    // Prepare subscription update data
    const subscriptionData = {
      stripeCustomerId:
        typeof session.customer === "string"
          ? session.customer
          : session.customer.id,
      stripeSubscriptionId: subscription.id,
      plan: plan._id,
      status: subscription.status,
      billingCycle:
        subscription.items.data[0]?.plan.interval === "year"
          ? "yearly"
          : "monthly",
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      lastPaymentDate: new Date(),
    };

    // Only set these fields if they are valid numbers
    if (typeof currentPeriodStart === "number" && !isNaN(currentPeriodStart)) {
      subscriptionData.currentPeriodStart = new Date(currentPeriodStart * 1000);
    }
    if (typeof currentPeriodEnd === "number" && !isNaN(currentPeriodEnd)) {
      subscriptionData.currentPeriodEnd = new Date(currentPeriodEnd * 1000);
    }

    // Update user subscription
    await User.findByIdAndUpdate(
      userId,
      {
        subscription: subscriptionData,
      },
      { new: true }
    );

    res.status(200).json({
      success: true,
      message: "Subscription activated successfully",
      subscription: {
        plan: plan.name,
        status: subscription.status,
        billingCycle: subscriptionData.billingCycle,
        currentPeriodEnd: subscriptionData.currentPeriodEnd,
      },
    });
  } catch (error) {
    console.error("Checkout verification error:", error);

    const statusCode = error.type === "StripeInvalidRequestError" ? 400 : 500;
    const message =
      error.type === "StripeInvalidRequestError"
        ? "Invalid checkout session"
        : "Internal server error";

    res.status(statusCode).json({
      success: false,
      message,
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Example of improved error handling for Stripe API calls
async function createStripeSubscription(customerId, priceId) {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: "default_incomplete",
      expand: ["latest_invoice.payment_intent"],
    });

    return {
      success: true,
      subscription,
    };
  } catch (error) {
    console.error("Stripe API error:", error);

    // Handle specific Stripe errors
    if (error.type === "StripeCardError") {
      return {
        success: false,
        status: 400,
        message: "Your card was declined. Please try another payment method.",
        error: error.message,
      };
    } else if (error.type === "StripeInvalidRequestError") {
      return {
        success: false,
        status: 400,
        message: "Invalid request to payment processor.",
        error: error.message,
      };
    }

    // Generic error
    return {
      success: false,
      status: 500,
      message: "An error occurred with the payment processor.",
      error: error.message,
    };
  }
}

// Then you can use this function in your controller methods

// Export the controller
module.exports = exports;


