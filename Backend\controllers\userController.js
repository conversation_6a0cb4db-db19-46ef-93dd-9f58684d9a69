const User = require('../models/user');
const TeamMember = require('../models/teamMember');
const Plan = require('../models/plan');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { transporter } = require("../config/nodemailer");

// Register a new user
exports.register = async (req, res, next) => {
  try {
    const { name, email, password } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ success: false, message: 'User with this email already exists' });
    }
    
    // Create new user
    const user = new User({
      name,
      email,
      password // Password will be hashed in the model's pre-save hook
    });
    
    await user.save();
    
    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    next(error);
  }
};

// Login user
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;
    
    // Find user by email
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
    
    res.status(200).json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get user profile
exports.getProfile = async (req, res, next) => {
  try {
    // Use ownerId if present, else fallback to user._id
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);
    res.status(200).json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
};

// Update user profile
exports.updateProfile = async (req, res, next) => {
  try {
    const { name, email } = req.body;
    const userId = req.ownerId || req.user.id;
    // Update user
    const user = await User.findByIdAndUpdate(
      userId,
      { name, email },
      { new: true, runValidators: true }
    );
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }
    res.status(200).json({
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    next(error);
  }
};

// Change password
exports.changePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    // Get user with password
    const user = await User.findById(req.user.id).select('+password');
    
    // Check current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }
    
    // Update password
    user.password = newPassword;
    await user.save();
    
    res.status(200).json({ message: 'Password changed successfully' });
  } catch (error) {
    next(error);
  }
};

// Forgot password
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;
    
    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Generate reset token (in a real app, you would send this via email)
    const resetToken = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    // In a real app, you would send an email with a reset link
    // For now, just return the token in the response
    res.status(200).json({
      message: 'Password reset token generated',
      resetToken
    });
  } catch (error) {
    next(error);
  }
};

// Reset password
exports.resetPassword = async (req, res, next) => {
  try {
    const { resetToken, newPassword } = req.body;
    
    // Verify reset token
    const decoded = jwt.verify(resetToken, process.env.JWT_SECRET);
    
    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Update password
    user.password = newPassword;
    await user.save();
    
    res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid or expired token' });
    }
    next(error);
  }
};

/**
 * Get user data with subscription-based filtering
 */
exports.getUserData = async (req, res) => {
  try {
    // The subscription data is already attached by the middleware
    const { plan, features, isFree } = req.subscriptionData;
    
    // Get the user with only the fields they should have access to
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Create a filtered user object based on subscription
    const filteredUser = {
      _id: user._id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      createdAt: user.createdAt,
      subscription: {
        planName: plan.name,
        status: req.subscriptionData.status,
        features: features.map(f => ({ key: f.key, name: f.name }))
      }
    };
    
    // Add additional fields based on subscription level
    if (!isFree) {
      // Add fields only available to paid subscribers
      filteredUser.youtubeConnected = user.youtubeConnected;
      filteredUser.lastLogin = user.lastLogin;
      
      // Add premium fields for specific plans
      const hasPremiumAccess = features.some(f => f.key === 'premium_analytics');
      if (hasPremiumAccess) {
        filteredUser.analytics = user.analytics;
        filteredUser.advancedStats = user.advancedStats;
      }
    }
    
    res.status(200).json({
      success: true,
      user: filteredUser
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user data',
      error: error.message
    });
  }
};

/**
 * Get user analytics - requires premium_analytics feature
 */
exports.getUserAnalytics = async (req, res) => {
  try {
    const userId = req.ownerId || req.user.id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // The subscription check middleware already verified the user has access to this feature
    
    // Get analytics data - this would typically come from your analytics service
    const analyticsData = user.analytics || {
      totalViews: 0,
      subscribers: 0,
      watchTime: 0,
      engagement: 0,
      recentVideos: []
    };
    
    res.status(200).json({
      success: true,
      analytics: analyticsData,
      planName: req.subscriptionData.plan.name
    });
  } catch (error) {
    console.error('Error fetching user analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: error.message
    });
  }
};

/**
 * Add/invite a team member
 */
exports.addTeamMember = async (req, res) => {
  try {
    const owner = req.ownerId || req.user.id;
    const { email } = req.body;
    console.log("Adding team member for owner:", owner, "with email:", email);
    
    if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      return res.status(400).json({ success: false, message: "Invalid email address BRO" });
    }

    // Check if the user is already a team member or invited
    const existing = await TeamMember.findOne({ owner, email });
    if (existing) {
      return res.status(400).json({ success: false, message: "This email is already invited or a team member." });
    }

    // Get owner's plan and team member limit
    const ownerUser = await User.findById(owner).populate('subscription.plan');
    const plan = ownerUser?.subscription?.plan;
    // If plan is free, teamLimit is 1 (owner only, no invites)
    const isFreePlan = plan?.name?.toLowerCase() === 'free';
    const teamLimit = plan?.features?.teamMembers ?? 1;

    // Prevent invite if free plan or teamMembers is 1 or less
    if (isFreePlan || !teamLimit || teamLimit <= 1) {
      return res.status(403).json({ success: false, message: "Your current plan does not allow inviting team members." });
    }

    // Count current team members (active or invited)
    const currentCount = await TeamMember.countDocuments({ owner, status: { $in: ["invited", "active"] } });
    if (currentCount >= teamLimit - 1) {
      // -1 because owner is always counted as 1
      return res.status(403).json({ success: false, message: "Team member limit reached for your plan." });
    }

    // Create the team member invitation (user may not exist yet)
    const teamMember = await TeamMember.create({
      owner,
      email,
      status: "invited"
    });

    // Optionally, send an invitation email
    transporter.sendMail({
      from: process.env.EMAIL_HOST_USER,
      to: [email],
      subject: "You've been invited to join a ReelScheduler team",
      html: `
        <div style="padding:24px; font-family:sans-serif;">
          <h2>You've been invited to join a team on ReelScheduler</h2>
          <p>${ownerUser.name || "A user"} has invited you to join their team.</p>
          <p>If you don't have an account, <a href="${process.env.FRONTEND_URL}/register?invite=${teamMember._id}">register here</a>.</p>
          <p>If you already have an account, <a href="${process.env.FRONTEND_URL}/team-invitations/accept/${teamMember._id}">accept the invitation</a>.</p>
        </div>
      `
    }).catch(err => {
      // Log but don't fail the request
      console.error("Failed to send team invite email:", err);
    });

    res.status(201).json({
      success: true,
      message: "Team member invited successfully",
      teamMember
    });
  } catch (error) {
    console.error("Add team member error:", error);
    res.status(500).json({ success: false, message: "Failed to add team member", error: error.message });
  }
};

/**
 * Get all team members for the current user
 */
exports.getTeamMembers = async (req, res) => {
  try {
    const owner = req.query.ownerId || req.user.id;

    // Get all team members for this owner, EXCLUDING removed members
    const teamMembers = await TeamMember.find({ owner, status: { $ne: "removed" } })
      .populate('user', 'name email profileImage')
      .populate('owner', 'name email profileImage')
      .select('owner email status user createdAt permissions') // Include permission field
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      teamMembers
    });
  } catch (error) {
    console.error("Get team members error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch team members", error: error.message });
  }
};

/**
 * Get Owner Permissions
 */
exports.getTeamMemberPermissions = async (req, res) => {
  try {
    const teamMemberId = req.params.id;
    if (!teamMemberId) {
      return res.status(400).json({ success: false, message: "Team member ID is required" });
    }

    // Get the team member's permissions (not owner's)
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, status: { $ne: "removed" } })
      .select('permissions');

    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }

    res.status(200).json({
      success: true,
      permissions: teamMember.permissions
    });
  } catch (error) {
    console.error("Get team member permissions error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch team member permissions", error: error.message });
  }
};

/**
 * Accept a team member invitation
 */
exports.acceptTeamMember = async (req, res) => {
  try {
    const { teamMemberId } = req.body;
    const userId = req.user.id;

    // Find the team member invitation
    const teamMember = await TeamMember.findById(teamMemberId);
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member invitation not found" });
    } 
    if (teamMember.status !== "invited") {
      return res.status(400).json({ success: false, message: "This invitation is no longer valid" });
    }
    if (teamMember.user && teamMember.user.toString() !== userId.toString()) {
      return res.status(403).json({ success: false, message: "You cannot accept this invitation" });
    }
    // Update the team member to active and link to the user
    teamMember.status = "active";
    teamMember.user = userId; // Link to the current user
    await teamMember.save();
    // Optionally, update the user's team members list
    const user = await User.findById(userId);
    if (!user.teamMembers) {  
      user.teamMembers = [];
    }
    if (!user.teamMembers.includes(teamMember._id)) {
      user.teamMembers.push(teamMember._id);
      await user.save();
    } 
    res.status(200).json({
      success: true,
      message: "Team member invitation accepted",
      teamMember
    });
  } catch (error) {
    console.error("Accept team member error:", error);
    res.status(500).json({ success: false, message: "Failed to accept team member invitation", error: error.message });
  }
};

/**
 * Update a team member's status
 */
exports.updateTeamMemberStatus = async (req, res) => {
  try {
    const { teamMemberId, status } = req.body;
    const owner = req.ownerId || req.user.id;
    const validStatuses = ["invited", "active", "removed"];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ success: false, message: "Invalid status" });
    }
    // Find the team member
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, owner });
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }
    // Update the status
    teamMember.status = status;
    await teamMember.save();
    // If status is active, link to the user if not already linked
    if (status === "active" && !teamMember.user) {
      teamMember.user = owner; // Link to the owner user
      await teamMember.save();
      // Optionally, add to user's team members list if not already present
      const user = await User.findById(owner);
      if (!user.teamMembers) {
        user.teamMembers = [];
      }
      if (!user.teamMembers.includes(teamMember._id)) {
        user.teamMembers.push(teamMember._id);
        await user.save();
      }
    } 
    res.status(200).json({
      success: true,
      message: "Team member status updated successfully",
      teamMember
    });
  } catch (error) {
    console.error("Update team member status error:", error);
    res.status(500).json({ success: false, message: "Failed to update team member status", error: error.message });
  }
};

/**
 * Remove a team member
 */
exports.removeTeamMember = async (req, res) => {
  try {
    // Get teamMemberId from URL param, not body
    const teamMemberId = req.params.id;
    const owner = req.ownerId || req.user.id;

    // Find the team member
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, owner });
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }
    // Remove from user's team members list if present
    const user = await User.findById(owner);
    if (user.teamMembers) {
      user.teamMembers = user.teamMembers.filter(id => id.toString() !== teamMember._id.toString());
      await user.save();
    }
    // Completely remove the team member document from DB
    await TeamMember.deleteOne({ _id: teamMemberId, owner });

    res.status(200).json({
      success: true,
      message: "Team member removed and deleted successfully"
    });
  } catch (error) {
    console.error("Remove team member error:", error);
    res.status(500).json({ success: false, message: "Failed to remove team member", error: error.message });
  }
};

/**
 * Remove a members from team member
 */
exports.removeMemberFromTeamMember = async (req, res) => {
  try {
    // Get teamMemberId from URL param, not body
    const teamMemberId = req.params.id;
    const user = req.user.id;

    // Find the team member
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, user });
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }
    // Remove from user's team members list if present
    const userData = await User.findById(user);
    if (userData.teamMembers) {
      userData.teamMembers = userData.teamMembers.filter(id => id.toString() !== teamMember._id.toString());
      await userData.save();
    }
    // Completely remove the team member document from DB
    await TeamMember.deleteOne({ _id: teamMemberId, user });

    res.status(200).json({
      success: true,
      message: "Team member removed and deleted successfully"
    });
  } catch (error) {
    console.error("Remove team member error:", error);
    res.status(500).json({ success: false, message: "Failed to remove team member", error: error.message });
  }
};

/**
 * Get team member details
 */
exports.getTeamMemberDetails = async (req, res) => {  
  try {
    const { teamMemberId } = req.params;
    const owner = req.ownerId || req.user.id;

    // Find the team member
    const teamMember = await TeamMember
      .findOne({ _id: teamMemberId, owner })
      .populate('user', 'name email profileImage'); // Populate user details if they exist
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }
    res.status(200).json({  
      success: true,
      teamMember: {
        id: teamMember._id,
        email: teamMember.email,
        status: teamMember.status,
        user: teamMember.user ? {
          id: teamMember.user._id,
          name: teamMember.user.name,
          email: teamMember.user.email,
          profileImage: teamMember.user.profileImage
        } : null,
        permissions: teamMember.permissions, // <-- Add this line
        createdAt: teamMember.createdAt
      }
    });
  } catch (error) {
    console.error("Get team member details error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch team member details", error: error.message });
  }
};

/**
 * Resend team member invitation email
 */
exports.resendTeamMemberInvite = async (req, res) => {
  try {
    const { teamMemberId } = req.body;
    const owner = req.ownerId || req.user.id;

    // Find the team member invitation
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, owner });
    if (!teamMember || teamMember.status !== "invited") {
      return res.status(404).json({ success: false, message: "Team member invitation not found or already accepted" });
    }

    // Send the invitation email again
    transporter.sendMail({
      from: process.env.EMAIL_HOST_USER,
      to: [teamMember.email],
      subject: "You've been invited to join a ReelScheduler team",
      html: `
        <div style="padding:24px; font-family:sans-serif;">
          <h2>You've been invited to join a team on ReelScheduler</h2>
          <p>${req.user.name || "A user"} has invited you to join their team.</p>
          <p>If you don't have an account, <a href="${process.env.FRONTEND_URL}/register?invite=${teamMember._id}">register here</a>.</p>
          <p>If you already have an account, <a href="${process.env.FRONTEND_URL}/team-invitations/accept/${teamMember._id}">accept the invitation</a>.</p>
        </div>
      `
    }).catch(err => {
      console.error("Failed to resend team invite email:", err);
      return res.status(500).json({ success: false, message: "Failed to resend invitation email", error: err.message });
    });

    res.status(200).json({
      success: true,
      message: "Invitation email resent successfully"
    });
  } catch (error) {
    console.error("Resend team member invite error:", error);
    res.status(500).json({ success: false, message: "Failed to resend team member invite", error: error.message });
  }
};


/**
 * Disconnect a team member (remove their access)
 */
exports.disconnectTeamMember = async (req, res) => {
  try {
    const { teamMemberId } = req.body;
    const owner = req.ownerId || req.user.id;

    // Find the team member
    const teamMember = await TeamMember.findOne({ _id: teamMemberId, owner });
    if (!teamMember) {
      return res.status(404).json({ success: false, message: "Team member not found" });
    }
    if (teamMember.status === "removed") {  
      return res.status(400).json({ success: false, message: "This team member has already been removed" });
    }
    // Update the status to removed
    teamMember.status = "removed";
    await teamMember.save();
    // Optionally, remove from user's team members list
    const user = await User.findById(owner);
    if (user.teamMembers) {
      user.teamMembers = user.teamMembers.filter(id => id.toString() !== teamMember._id.toString());
      await user.save();
    }
    res.status(200).json({
      success: true,
      message: "Team member disconnected successfully",
      teamMember
    });
  } catch (error) {
    console.error("Disconnect team member error:", error);
    res.status(500).json({ success: false, message: "Failed to disconnect team member", error: error.message });
  }
};

/**
 * Change team member permission
 */
exports.changeTeamMemberPermission = async (req, res) => {
  try {
    const { id } = req.params;
    // Accept 'permissions' (array) from body
    const { permissions } = req.body;
    const owner = req.ownerId || req.user.id;

    // Valid permissions list
    const validPermissions = [
      "admin",
      "upload",
      "edit",
      "delete",
      "view",
      "analytics",
      "manageBundles",
      "manageSchedules",
      "manageVideos",
      "manageTeam"
    ];

    // Validate
    if (
      !Array.isArray(permissions) ||
      permissions.some(p => !validPermissions.includes(p))
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid permission level"
      });
    }

    // Update the team member's permissions (now an array)
    const teamMember = await TeamMember.findOneAndUpdate(
      { _id: id, owner },
      { permissions },
      { new: true, runValidators: true }
    ).populate('user', 'name email');

    if (!teamMember) {
      return res.status(404).json({
        success: false,
        message: "Team member not found or you don't have permission"
      });
    }

    res.status(200).json({
      success: true,
      message: "Permission updated successfully",
      teamMember
    });
  } catch (error) {
    console.error("Permission update error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update permission",
      error: error.message
    });
  }
};

/**
 * Get all owners for whom the current user is a team member
 */
exports.getMyOwners = async (req, res) => {
  try {
    // Find all team memberships for this user (active or invited)
    const teamMemberships = await TeamMember.find({
      user: req.user.id,
      status: { $in: ["active", "invited"] }
    }).populate('owner', 'name email profileImage');

    // Extract unique owners
    const owners = teamMemberships
      .filter(tm => tm.owner) // Only if owner exists
      .map(tm => ({
        id: tm.owner._id,
        name: tm.owner.name,
        email: tm.owner.email,
        profileImage: tm.owner.profileImage,
        teamMemberStatus: tm.status,
        teamMemberId: tm._id
      }));

    res.status(200).json({
      success: true,
      owners
    });
  } catch (error) {
    console.error("Get my owners error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch owners", error: error.message });
  }
};