const { google } = require('googleapis');
const http = require('http');
const url = require('url');
const fs = require('fs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function authenticateWithGoogle(UserId, CLIENT_ID, CLIENT_SECRET, API_KEY) {
  try {
    // Get client credentials from environment or client secret file
    let clientId, clientSecret, redirectUri;
    try {
      const clientSecretFile = './client_secret_662670870317-7apukjq16hv58d4f4m39hb5jojtnaduk.apps.googleusercontent.com.json';
      const clientSecretData = JSON.parse(fs.readFileSync(clientSecretFile, 'utf8'));
      clientId = clientSecretData.web.client_id;
      clientSecret = clientSecretData.web.client_secret;
      
      console.log('Using credentials from client secret file');
    } catch (error) {
      // Prioritize passed parameters over environment variables
      clientId = CLIENT_ID || process.env.GOOGLE_CLIENT_ID;
      clientSecret = CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET;
      console.log('Using credentials from parameters or environment variables');
    }

    // Use a fixed port for OAuth callback
    const PORT = 8080;
    const CALLBACK_PATH = '/oauth2callback';
    redirectUri = `http://localhost:${PORT}${CALLBACK_PATH}`;

    console.log('Client ID:', clientId);
    console.log('Redirect URI:', redirectUri);
    console.log('Port:', PORT);
    console.log('Callback Path:', CALLBACK_PATH);
    console.log('\nIMPORTANT: Make sure this exact redirect URI is added to your Google Cloud Console!');

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      redirectUri
    );

    // Generate auth URL with unique state to prevent CSRF
    const state = Math.random().toString(36).substring(2, 15);
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/youtube',
        'https://www.googleapis.com/auth/youtube.upload',
        'https://www.googleapis.com/auth/youtube.force-ssl'
      ],
      prompt: 'consent', // Force to get refresh token
      state: state
    });

    // Log the auth URL instead of opening it automatically
    console.log('\n🔗 Please open this URL in your browser to authenticate:');
    console.log(authUrl);
    console.log('\n📝 Copy and paste the URL above into your browser to continue the authentication process.');

    // Create a promise to handle the authentication flow
    return new Promise((resolve, reject) => {
      // Create a server to handle the callback
      const server = http.createServer(async (req, res) => {
        try {
          const parsedUrl = url.parse(req.url, true);
          const pathname = parsedUrl.pathname;
          
          if (pathname === CALLBACK_PATH) {
            const queryObject = parsedUrl.query;
            
            // Log the received query parameters
            console.log('Received callback with query:', queryObject);
            
            // Verify state parameter to prevent CSRF
            if (queryObject.state !== state) {
              console.error('State parameter mismatch, possible CSRF attack');
              res.writeHead(400, { 'Content-Type': 'text/html' });
              res.end('<h1>Authentication failed!</h1><p>Invalid state parameter.</p>');
              reject(new Error('Invalid state parameter'));
              return;
            }
            
            if (queryObject.code) {
              try {
                // Exchange code for tokens
                console.log('Exchanging code for tokens...');
                const { tokens } = await oauth2Client.getToken(queryObject.code);
                console.log('Received tokens:', {
                  access_token: tokens.access_token ? 'present' : 'missing',
                  refresh_token: tokens.refresh_token ? 'present' : 'missing',
                  expiry_date: tokens.expiry_date
                });
                
                // Set credentials
                oauth2Client.setCredentials(tokens);
                
                // Get user info
                const oauth2 = google.oauth2({
                  auth: oauth2Client,
                  version: 'v2'
                });
                
                try {
                  const userInfo = await oauth2.userinfo.get();
                  console.log('User info retrieved:', userInfo.data.email);
                } catch (userInfoError) {
                  console.error('Error getting user info:', userInfoError.message);
                }
                
                // Try to get YouTube channel info
                try {
                  const youtube = google.youtube({
                    version: 'v3',
                    auth: oauth2Client
                  });
                  
                  const channelResponse = await youtube.channels.list({
                    part: 'snippet,statistics',
                    mine: true
                  });
                  
                  if (channelResponse.data.items && channelResponse.data.items.length > 0) {
                    const channel = channelResponse.data.items[0];
                    
                    // Add channel info to tokens object
                    tokens.channelInfo = {
                      id: channel.id,
                      title: channel.snippet.title,
                      description: channel.snippet.description,
                      customUrl: channel.snippet.customUrl,
                      thumbnail: channel.snippet.thumbnails ? 
                        (channel.snippet.thumbnails.default ? channel.snippet.thumbnails.default.url : null) : null,
                      statistics: channel.statistics ? {
                        subscriberCount: channel.statistics.subscriberCount,
                        videoCount: channel.statistics.videoCount,
                        viewCount: channel.statistics.viewCount
                      } : null
                    };
                    
                    console.log('Channel info added to tokens:', tokens.channelInfo.title);
                  } else {
                    console.log('No YouTube channel found for this account');
                  }
                } catch (channelError) {
                  console.error('Error fetching channel info:', channelError.message);
                  // Continue even if we can't get channel info
                }
                
                // Save tokens to a file
                fs.writeFileSync(
                  './google-tokens.json', 
                  JSON.stringify(tokens, null, 2)
                );
                console.log('Tokens and channel info saved to google-tokens.json');
                
                // Send success response
                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(`
                  <h1>Authentication successful!</h1>
                  <p>You can close this window and return to the application.</p>
                  <p>Tokens and channel info have been saved to google-tokens.json</p>
                  <script>
                    // Close window after 3 seconds
                    setTimeout(() => {
                      window.close();
                    }, 3000);
                  </script>
                `);
                
                // Close the server after a short delay
                setTimeout(() => {
                  server.close();
                  console.log('Server closed. You can now use the tokens in google-tokens.json.');
                  resolve({ success: true, tokens });
                }, 2000);
              } catch (tokenError) {
                console.error('Error exchanging code for tokens:', tokenError.message);
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end(`<h1>Authentication error!</h1><p>Error: ${tokenError.message}</p>`);
                reject(tokenError);
              }
            } else {
              // Handle error
              res.writeHead(400, { 'Content-Type': 'text/html' });
              res.end('<h1>Authentication failed!</h1><p>No authorization code received. Please try again.</p>');
              reject(new Error('No authorization code received'));
            }
          } else {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('<h1>404 Not Found</h1>');
          }
        } catch (error) {
          console.error('Server error:', error);
          res.writeHead(500, { 'Content-Type': 'text/html' });
          res.end(`<h1>Server Error</h1><p>Error: ${error.message}</p>`);
          reject(error);
        }
      });

      // Start the server
      server.listen(PORT, () => {
        console.log(`OAuth callback server listening on port ${PORT}`);
        console.log(`Waiting for authentication callback at: http://localhost:${PORT}${CALLBACK_PATH}`);
      });

      // Handle server errors
      server.on('error', (error) => {
        if (error.code === 'EADDRINUSE') {
          console.error(`Port ${PORT} is already in use. Please close any applications using this port and try again.`);
          reject(new Error(`Port ${PORT} is already in use`));
        } else {
          console.error('Server error:', error);
          reject(error);
        }
      });

      // Set a timeout for the authentication process
      setTimeout(() => {
        server.close();
        reject(new Error('Authentication timeout. Please try again.'));
      }, 300000); // 5 minutes timeout
    });
  } catch (error) {
    console.error('Authentication setup error:', error);
    throw error;
  }
}

module.exports = { authenticateWithGoogle };








