{"level":"error","message":"Unhandled Promise Rejection: Connection.prototype.close() no longer accepts a callback","service":"reel-scheduler-api","stack":"MongooseError: Connection.prototype.close() no longer accepts a callback\n    at NativeConnection.close (C:\\Users\\<USER>\\OneDrive\\Documents\\My Projects\\ReelScheduler\\Backend\\node_modules\\mongoose\\lib\\connection.js:1205:11)\n    at Server.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\My Projects\\ReelScheduler\\Backend\\index.js:218:29)\n    at Object.onceWrapper (node:events:633:28)\n    at Server.emit (node:events:519:28)\n    at emitCloseNT (node:net:2340:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:81:21)","timestamp":"2025-08-27 03:37:32"}
{"level":"error","message":"Unhandled Promise Rejection: Connection.prototype.close() no longer accepts a callback","service":"reel-scheduler-api","stack":"MongooseError: Connection.prototype.close() no longer accepts a callback\n    at NativeConnection.close (C:\\Users\\<USER>\\OneDrive\\Documents\\My Projects\\ReelScheduler\\Backend\\node_modules\\mongoose\\lib\\connection.js:1205:11)\n    at Server.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\My Projects\\ReelScheduler\\Backend\\index.js:220:29)\n    at Object.onceWrapper (node:events:633:28)\n    at Server.emit (node:events:519:28)\n    at emitCloseNT (node:net:2340:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:81:21)","timestamp":"2025-08-27 03:44:35"}
