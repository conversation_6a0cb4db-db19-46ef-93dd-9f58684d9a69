const User = require('../models/user');
const Plan = require('../models/plan');

/**
 * Middleware to check if a user has the required subscription features
 * @param {string} feature - The specific feature to check (optional)
 * @param {Function} checkFn - Custom function to check subscription requirements
 * @returns {Function} Express middleware
 */
const checkSubscription = (feature, checkFn) => {
  return async (req, res, next) => {
    try {
      // Get the authenticated user from the request
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }
      
      // Check if the user's subscription is active (skip for free users)
      if (user.subscription && user.subscription.plan && user.subscription.status !== 'active') {
        return res.status(403).json({
          success: false,
          message: 'Your subscription is not active',
          subscriptionStatus: user.subscription.status
        });
      }
      
      // If a custom check function is provided, use it
      if (checkFn && typeof checkFn === 'function') {
        const hasAccess = await checkFn(user, feature, req);
        
        if (!hasAccess) {
          // Determine the appropriate error message based on the feature
          let message = 'Your current plan does not allow this action';
          
          if (feature === 'videoUploadsLimit') {
            message = 'You have reached your monthly video upload limit';
          } else if (feature === 'storageLimit') {
            message = 'You have reached your storage limit';
          } else if (feature === 'scheduleLimit') {
            message = 'You have reached your active schedule limit';
          } else if (feature === 'bundleLimit') {
            message = 'You have reached your bundle limit';
          } else if (feature === 'bulkUpload') {
            message = 'Bulk upload is not available on your current plan';
          }
          
          return res.status(403).json({
            success: false,
            message,
            upgradeRequired: true,
            feature
          });
        }
      }
      
      // If all checks pass, proceed to the next middleware
      next();
    } catch (error) {
      console.error('Error in checkSubscription middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while checking subscription'
      });
    }
  };
};

module.exports = checkSubscription;


