const TeamMember = require("../models/teamMember");
const User = require("../models/user");

module.exports = async function resolveOwnerUser(req, res, next) {
  try {
    const userId = req.user.id;

    // Find all active team memberships for this user
    const teamMemberships = await TeamMember.find({ user: userId, status: "active" });
    if (teamMemberships && teamMemberships.length > 0) {
      // Multiple owners possible
      const ownerIds = teamMemberships.map(tm => tm.owner);
      const owners = await User.find({ _id: { $in: ownerIds } });

      req.isTeamMember = true;
      req.owners = owners;
      req.teamMemberships = teamMemberships;

      // For backward compatibility, set the first as default
      req.ownerUser = owners[0];
      req.ownerId = owners[0]?._id;
      req.teamMembership = teamMemberships[0];
    } else {
      req.isTeamMember = false;
      req.owners = [];
      req.teamMemberships = [];
      req.ownerUser = req.user;
      req.ownerId = req.user.id;
      req.teamMembership = null;
    }
    next();
  } catch (err) {
    next(err);
  }
};