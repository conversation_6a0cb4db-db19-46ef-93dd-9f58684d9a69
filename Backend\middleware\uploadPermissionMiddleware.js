const { checkUploadPermission } = require('../services/userPlanService');
const TeamMember = require('../models/teamMember');

/**
 * Middleware to check upload permissions for the current user or team member
 */
exports.checkUploadPermission = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId;
    const userId = ownerId || req.user.id;

    // Check if user is uploading for someone else (team member scenario)
    if (ownerId && ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });

      if (!teamMembership) {
        return res.status(403).json({
          success: false,
          message: "You do not have access to upload videos for this account.",
          permission: false,
        });
      }

      // Check if team member has upload permission
      if (!Array.isArray(teamMembership.permissions) || 
          !teamMembership.permissions.includes("upload")) {
        return res.status(403).json({
          success: false,
          message: "You do not have permission to upload videos for this account.",
          permission: false,
        });
      }
    }

    // Check upload limits for the target user (owner)
    const uploadPermission = await checkUploadPermission(userId);

    if (!uploadPermission.canUpload) {
      return res.status(403).json({
        success: false,
        message: uploadPermission.message || "Upload limit exceeded",
        upgradeRequired: true,
        feature: "videoUploadsLimit",
        currentCount: uploadPermission.currentCount,
        uploadLimit: uploadPermission.uploadLimit,
        resetDate: uploadPermission.resetDate,
        plan: uploadPermission.plan
      });
    }

    // Add upload permission info to request for later use
    req.uploadPermission = uploadPermission;
    next();
  } catch (error) {
    console.error('Upload permission middleware error:', error);
    return res.status(500).json({
      success: false,
      message: "Error checking upload permissions",
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user can view videos (for team members)
 */
exports.checkViewPermission = async (req, res, next) => {
  try {
    const ownerId = req.query.ownerId;
    
    // If no ownerId specified, user is viewing their own videos
    if (!ownerId) {
      return next();
    }

    // Check if user is viewing someone else's videos (team member scenario)
    if (ownerId !== req.user.id.toString()) {
      const teamMembership = await TeamMember.findOne({
        user: req.user.id,
        owner: ownerId,
        status: "active",
      });

      if (!teamMembership) {
        return res.status(403).json({
          success: false,
          message: "You do not have access to view videos for this account.",
          permission: false,
        });
      }

      // Check if team member has view permission
      if (!Array.isArray(teamMembership.permissions) || 
          !teamMembership.permissions.includes("view")) {
        return res.status(403).json({
          success: false,
          message: "You do not have permission to view videos for this account.",
          permission: false,
        });
      }
    }

    next();
  } catch (error) {
    console.error('View permission middleware error:', error);
    return res.status(500).json({
      success: false,
      message: "Error checking view permissions",
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}; 