module.exports = async function validatePermissionChange(req, res, next) {
    const { permission, permissions } = req.body;
    // Accept either 'permissions' (array) or 'permission' (legacy string)
    const validPermissions = [
        "admin",
        "upload",
        "edit",
        "delete",
        "view",
        "analytics",
        "manageBundles",
        "manageSchedules",
        "manageVideos",
        "manageTeam"
    ];

    // Prefer 'permissions' array, fallback to 'permission' string for legacy
    let perms = Array.isArray(permissions)
        ? permissions
        : (typeof permission === "string" ? [permission] : []);

    // Validate all permissions
    if (
        !Array.isArray(perms) ||
        perms.some(p => !validPermissions.includes(p))
    ) {
        return res.status(400).json({
            success: false,
            message: "Invalid permission level"
        });
    }

    next();
};