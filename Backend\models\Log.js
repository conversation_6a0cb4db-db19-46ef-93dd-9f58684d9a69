const mongoose = require('mongoose');

const LogSchema = new mongoose.Schema({
  level: {
    type: String,
    enum: ['error', 'warn', 'info', 'debug'],
    required: true
  },
  message: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  meta: {
    user: String,
    ip: String,
    method: String,
    url: String,
    status: Number,
    responseTime: Number,
    userAgent: String
  }
});

module.exports = mongoose.model('Log', LogSchema);