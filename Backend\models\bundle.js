const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const bundleSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      minlength: 1,
      maxlength: 100,
    },
    description: {
      type: String,
      required: false,
      default: null,
    },
    videos: [
      {
        videoId: {
          type: Schema.Types.ObjectId,
          ref: "Video",
          required: true,
        },
        fileName: {
          type: String,
          default: null,
        },
        thumbnailUrl: { type: String, default: null },
        status: {
          type: String,
          enum: ["uploaded", "pending", "processing", "failed", "ready"],
          default: "ready",
        },
        order: { type: Number, required: true },
      },
    ],
  },
  {
    timestamps: true,
  }
);

const Bundle = mongoose.model("Bundle", bundleSchema);

module.exports = Bundle;
