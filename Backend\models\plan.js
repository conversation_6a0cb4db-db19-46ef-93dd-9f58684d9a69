const mongoose = require('mongoose');

const planSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  monthlyPrice: {
    type: Number,
    required: true
  },
  yearlyPrice: {
    type: Number,
    required: true
  },
  price: {
    monthly: {
      type: Number,
      required: true
    },
    yearly: {
      type: Number,
      required: true
    }
  },
  monthlyPriceId: {
    type: String
  },
  yearlyPriceId: {
    type: String
  },
  features: {
    videoUploadsLimit: {
      type: Number,
      default: 5
    },
    videoStorageDays: {
      type: Number,
      default: 30
    },
    storageLimit: {
      type: Number,
      default: 100 // MB
    },
    bundleLimit: {
      type: Number,
      default: 2
    },
    scheduleLimit: {
      type: Number,
      default: 1
    },
    schedulingOptions: {
      type: [String],
      default: ['basic']
    },
    prioritySupport: {
      type: Boolean,
      default: false
    },
    bulkUpload: {
      type: Boolean,
      default: false
    },
    teamMembers: {
      type: Number,
      default: 1 // Free plan: 1 means only owner, no invites allowed
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

const Plan = mongoose.model('Plan', planSchema);

module.exports = Plan;


