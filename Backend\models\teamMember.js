const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const teamMemberSchema = new Schema(
  {
    owner: { type: Schema.Types.ObjectId, ref: "User", required: true },
    user: { type: Schema.Types.ObjectId, ref: "User", required: false },
    email: { type: String, required: true },
    status: {
      type: String,
      enum: ["invited", "active", "removed"],
      default: "invited",
    },
    permissions: {
      type: [String],
      enum: [
        "admin",
        "upload",
        "edit",
        "delete",
        "view",
        "analytics",
        "manageBundles",
        "manageSchedules",
        "manageVideos",
        "manageTeam",
      ],
      default: ["view"],
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("TeamMember", teamMemberSchema);
