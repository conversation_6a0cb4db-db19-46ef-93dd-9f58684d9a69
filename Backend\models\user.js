const mongoose = require("mongoose");
const bcrypt = require("bcrypt");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
      select: false, // Hide password from query results
    },
    role: {
      type: String,
      enum: ["user", "admin", "developer"],
      default: "user",
    },
    uploadCountCurrentMonth: {
      type: Number,
      default: 0,
    },
    lastResetMonth: {
      type: String,
      default: "",
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    instagramUsername: String,
    instagramPassword: {
      type: String,
      select: false,
    },
    verificationToken: String,
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    subscription: {
      plan: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Plan",
        index: true,
      },
      status: {
        type: String,
        enum: [
          "active",
          "canceled",
          "past_due",
          "unpaid",
          "incomplete",
          "incomplete_expired",
          "trialing",
        ],
        default: "active",
      },
      stripeCustomerId: String,
      stripeSubscriptionId: String,
      currentPeriodStart: Date,
      currentPeriodEnd: Date,
      cancelAtPeriodEnd: {
        type: Boolean,
        default: false,
      },
      trialEnd: Date,
      customFields: {
        type: Map,
        of: String,
      },
    },
    // Updated YouTube connection fields to match controller usage
    youtubeConnected: {
      type: Boolean,
      default: false,
    },
    googleAccessToken: String,
    googleRefreshToken: String,
    youtubeTokenExpiry: Date,
    youtubeChannelId: String,
    youtubeChannelName: String,
    youtubeChannelTitle: String,
    youtubeChannelThumbnail: String,
    // Facebook connection fields
    facebookConnected: {
      type: Boolean,
      default: false,
    },
    facebookName: String,
    facebookPicture: String,
    facebookAccessToken: String,
    facebookUserId: String,
    facebookPageId: String,
    facebookPageName: String,
    facebookPageAccessToken: String,
    facebookPagePicture: String,
    facebookTokenExpiry: Date,
    // Add teamMembers array for team member relationships
    teamMembers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "TeamMember"
    }],
    createdAt: {
      type: Date,
      default: Date.now,
    },
    status: {
        type: String,
        enum: [
          "active",
          "inactive",
          "suspend"
        ],
        default: "active",
      },
      timezone: {
        type: String,
        default: "UTC"
      },
  },
  { timestamps: true }
);

// Hash password before saving
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model("User", userSchema);

module.exports = User;
