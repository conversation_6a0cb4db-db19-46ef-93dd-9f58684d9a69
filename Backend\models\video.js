const mongoose = require("mongoose");

const videoSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    fileSize: {
      type: Number,
    },
    duration: {
      type: Number,
    },
    status: {
      type: String,
      enum: ["processing", "ready", "error", "uploaded", "scheduled"],
      default: "ready",
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    bundleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Bundle",
    },
    videoUrls: [
      {
        url: String,
        isUploaded: {
          type: Boolean,
          default: false,
        },
        thumbnailUrl: { type: String, default: null },
        platform: {
          type: String,
          enum: ["youtube", "appwrite", "local", "b2", "imagekit"],
          default: "appwrite",
        },
        uploadDate: Date,
        fileName: String,
        fileId: String,
      },
    ],
    appwriteFileId: {
      type: String,
    },
    appwriteUrl: {
      type: String,
    },
    youtubeVideoId: {
      type: String,
    },
    youtubeUrl: {
      type: String,
    },
    thumbnailUrl: {
      type: String,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Add index for faster queries
videoSchema.index({ userId: 1, createdAt: -1 });
videoSchema.index({ bundleId: 1 });

module.exports = mongoose.model("Video", videoSchema);
