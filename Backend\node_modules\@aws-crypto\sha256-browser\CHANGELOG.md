# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [5.2.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v5.1.0...v5.2.0) (2023-10-16)

### Features

- support ESM artifacts in all packages ([#752](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/752)) ([e930ffb](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/e930ffba5cfef66dd242049e7d514ced232c1e3b))

# [5.1.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v5.0.0...v5.1.0) (2023-09-22)

### Bug Fixes

- Update tsc to 2.x ([#735](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/735)) ([782e0de](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/782e0de9f5fef41f694130580a69d940894b6b8c))

### Features

- Use @smithy/util-utf8 ([#730](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/730)) ([00fb851](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/00fb851ca3559d5a1f370f9256814de1210826b8)), closes [#699](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/699)

# [5.0.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v4.0.1...v5.0.0) (2023-07-13)

- feat!: drop support for IE 11 (#629) ([6c49fb6](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/6c49fb6c1b1f18bbff02dbd77a37a21bdb40c959)), closes [#629](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/629)

### BREAKING CHANGES

- Remove support for IE11

Co-authored-by: texastony <<EMAIL>>

# [4.0.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v3.0.0...v4.0.0) (2023-02-20)

**Note:** Version bump only for package @aws-crypto/sha256-browser

# [3.0.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v2.0.2...v3.0.0) (2023-01-12)

### Bug Fixes

- **docs:** sha256 packages, clarify hmac support ([#455](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/455)) ([1be5043](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/1be5043325991f3f5ccb52a8dd928f004b4d442e))

- feat!: replace Hash implementations with Checksum interface (#492) ([da43dc0](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/da43dc0fdf669d9ebb5bfb1b1f7c79e46c4aaae1)), closes [#492](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/492)

### BREAKING CHANGES

- All classes that implemented `Hash` now implement `Checksum`.

## [2.0.2](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v2.0.1...v2.0.2) (2022-09-07)

### Bug Fixes

- **#337:** update @aws-sdk/types ([#373](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/373)) ([b26a811](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/b26a811a392f5209c7ec7e57251500d4d78f97ff)), closes [#337](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/337)

## [2.0.1](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v2.0.0...v2.0.1) (2021-12-09)

**Note:** Version bump only for package @aws-crypto/sha256-browser

# [2.0.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v1.2.2...v2.0.0) (2021-10-25)

**Note:** Version bump only for package @aws-crypto/sha256-browser

## [1.2.2](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v1.2.1...v1.2.2) (2021-10-12)

**Note:** Version bump only for package @aws-crypto/sha256-browser

## [1.2.1](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v1.2.0...v1.2.1) (2021-09-17)

**Note:** Version bump only for package @aws-crypto/sha256-browser

# [1.2.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/v1.1.1...v1.2.0) (2021-09-17)

### Features

- add @aws-crypto/util ([8f489cb](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/8f489cbe4c0e134f826bac66f1bf5172597048b9))

## [1.1.1](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@1.1.0...@aws-crypto/sha256-browser@1.1.1) (2021-07-13)

### Bug Fixes

- **sha256-browser:** throw errors not string ([#194](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/194)) ([7fa7ac4](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/7fa7ac445ef7a04dfb1ff479e7114aba045b2b2c))

# [1.1.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@1.0.0...@aws-crypto/sha256-browser@1.1.0) (2021-01-13)

### Bug Fixes

- remove package lock ([6002a5a](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/6002a5ab9218dc8798c19dc205d3eebd3bec5b43))
- **aws-crypto:** export explicit dependencies on [@aws-types](https://github.com/aws-types) ([6a1873a](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/6a1873a4dcc2aaa4a1338595703cfa7099f17b8c))
- **deps-dev:** move @aws-sdk/types to devDependencies ([#188](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/188)) ([08efdf4](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/08efdf46dcc612d88c441e29945d787f253ee77d))

# [1.0.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@1.0.0-alpha.0...@aws-crypto/sha256-browser@1.0.0) (2020-10-22)

**Note:** Version bump only for package @aws-crypto/sha256-browser

# [1.0.0-alpha.0](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@0.1.0-preview.4...@aws-crypto/sha256-browser@1.0.0-alpha.0) (2020-02-07)

**Note:** Version bump only for package @aws-crypto/sha256-browser

# [0.1.0-preview.4](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@0.1.0-preview.2...@aws-crypto/sha256-browser@0.1.0-preview.4) (2020-01-16)

### Bug Fixes

- Changed package.json files to point to the right Git repo ([#9](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/9)) ([028245d](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/028245d72e642ca98d82226afb300eb154503c4a)), closes [#8](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/8)
- es2015.iterable required ([#10](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/10)) ([6e08d83](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/6e08d83c33667ad8cbeeaaa7cedf1bbe05f79ed8))
- lerna version maintains package-lock ([#14](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/14)) ([2ef29e1](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/2ef29e13779703a5c9b32e93d18918fcb33b7272)), closes [#13](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/13)

# [0.1.0-preview.3](https://github.com/aws/aws-sdk-js-crypto-helpers/compare/@aws-crypto/sha256-browser@0.1.0-preview.2...@aws-crypto/sha256-browser@0.1.0-preview.3) (2019-11-15)

### Bug Fixes

- Changed package.json files to point to the right Git repo ([#9](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/9)) ([028245d](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/028245d72e642ca98d82226afb300eb154503c4a)), closes [#8](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/8)
- es2015.iterable required ([#10](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/10)) ([6e08d83](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/6e08d83c33667ad8cbeeaaa7cedf1bbe05f79ed8))
- lerna version maintains package-lock ([#14](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/14)) ([2ef29e1](https://github.com/aws/aws-sdk-js-crypto-helpers/commit/2ef29e13779703a5c9b32e93d18918fcb33b7272)), closes [#13](https://github.com/aws/aws-sdk-js-crypto-helpers/issues/13)

# [0.1.0-preview.2](https://github.com/aws/aws-javascript-crypto-helpers/compare/@aws-crypto/sha256-browser@0.1.0-preview.1...@aws-crypto/sha256-browser@0.1.0-preview.2) (2019-10-30)

### Bug Fixes

- remove /src/ from .npmignore (for sourcemaps) ([#5](https://github.com/aws/aws-javascript-crypto-helpers/issues/5)) ([ec52056](https://github.com/aws/aws-javascript-crypto-helpers/commit/ec52056))
